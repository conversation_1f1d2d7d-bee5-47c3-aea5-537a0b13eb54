import CustomSubTitle from '@/components/CustomSubTitle';
import CustomTitle from '@/components/CustomTitle';
import EnergyNetworkMap from './EnergyNetworkMap';
import GasSituation from './GasSituation';
import HotSituation from './HotSituation';
import styles from './index.sass';
import RunSituation from './RunSituation';
import RunStatus from './RunStatus';
import UserTable from './UserTable';
import Warning from './Warning';

export default function () {
  return (
    <div className={styles.gridLayout}>
      <div className={styles.gridItem}>
        <CustomTitle>三联供电厂运行状态</CustomTitle>
        <RunStatus />
      </div>
      <div className={styles.gridItem}>
        <CustomTitle>园区能源网络地图</CustomTitle>
        <EnergyNetworkMap />
      </div>
      <div className={styles.gridItem}>
        <CustomTitle>热用户实时用热情况</CustomTitle>
        <HotSituation />
      </div>
      <div className={styles.gridItem}>
        <CustomTitle>气用户实时用气情况</CustomTitle>
        <GasSituation />
      </div>
      <div className={styles.gridItem}>
        <CustomSubTitle>气热用户分类列表</CustomSubTitle>
        <UserTable />
      </div>
      <div className={styles.gridItem}>
        <CustomSubTitle>历史预警信息</CustomSubTitle>
        <Warning />
      </div>
      <div className={styles.gridItem}>
        <CustomTitle>多能储能运行情况</CustomTitle>
        <div className={styles.energySummary}>
          {/* 储氢 */}
          <div className={styles.energyCol}>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>压力</span>
              <span className={styles.energyValue}>48</span>
              <span className={styles.energyUnit}>bar</span>
              <span className={styles.energyStatus}>(正常)</span>
            </div>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>流量</span>
              <span className={styles.energyArrowUp}>↑</span>
              <span className={styles.energyValue}>20</span>
              <span className={styles.energyUnit}>m³/h</span>
              <span className={styles.energyArrowDown}>↓</span>
              <span className={styles.energyValueCompare}>15</span>
              <span className={styles.energyUnit}>m³/h</span>
            </div>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>储量</span>
              <span className={styles.energyValue}>320</span>
              <span className={styles.energyUnit}>/500m³</span>
            </div>
          </div>
          {/* 储气 */}
          <div className={styles.energyCol}>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>压力</span>
              <span className={styles.energyValue}>78</span>
              <span className={styles.energyUnit}>bar</span>
              <span className={styles.energyStatus}>(正常)</span>
            </div>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>流量</span>
              <span className={styles.energyArrowUp}>↑</span>
              <span className={styles.energyValue}>20</span>
              <span className={styles.energyUnit}>m³/h</span>
              <span className={styles.energyArrowDown}>↓</span>
              <span className={styles.energyValueCompare}>15</span>
              <span className={styles.energyUnit}>m³/h</span>
            </div>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>储量</span>
              <span className={styles.energyValue}>720</span>
              <span className={styles.energyUnit}>/1000m³</span>
            </div>
          </div>
          {/* 储热 */}
          <div className={styles.energyCol}>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>温度</span>
              <span className={styles.energyValue}>280</span>
              <span className={styles.energyUnit}>℃</span>
              <span className={styles.energyStatus}>(正常)</span>
            </div>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>流量</span>
              <span className={styles.energyArrowUp}>↑</span>
              <span className={styles.energyValue}>50</span>
              <span className={styles.energyUnit}>GJ/h</span>
              <span className={styles.energyArrowDown}>↓</span>
              <span className={styles.energyValueCompare}>15</span>
              <span className={styles.energyUnit}>GJ/h</span>
            </div>
            <div className={styles.energyRow}>
              <span className={styles.energyLabel}>储量</span>
              <span className={styles.energyValue}>1450</span>
              <span className={styles.energyUnit}>/2000GJ</span>
            </div>
          </div>
        </div>
        <RunSituation />
      </div>
    </div>
  );
}
