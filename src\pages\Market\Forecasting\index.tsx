import { Button, ConfigProvider, DatePicker } from 'antd';
import Carbon from './Carbon';
import styles from './index.sass';
import MultLoad from './MultLoad';
import NewEnergy from './NewEnergy';
export default function () {
  return (
    <ConfigProvider
      theme={{
        components: {},
      }}
    >
      <div className={styles.box}>
        <div className={styles.time}>
          预测时间范围选择：
          <DatePicker.RangePicker></DatePicker.RangePicker>
          <Button className={styles.button} type="primary">
            查询
          </Button>
        </div>
        <div className={styles.top}>
          <div className={styles.item1}>
            <div className={styles.title}>新能源出力预测</div>
            <NewEnergy></NewEnergy>
          </div>
          <div className={styles.item2}>
            <div className={styles.title}>多能负荷预测</div>
            <MultLoad></MultLoad>
          </div>
        </div>
        <div className={styles.bottom}>
          <div className={styles.item3}>
            <div className={styles.title}>碳减排潜力预测曲线</div>
            <Carbon></Carbon>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
}
