{"name": "vpp_umi", "private": true, "author": "ttc <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky install", "setup": "max setup", "start": "set PORT=8200 && npm run dev"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.19.1", "@umijs/max": "^4.4.6", "@unocss/cli": "^0.51.12", "ahooks": "^3.7.8", "antd": "^5.14.0", "axios": "^1.5.0", "classnames": "^2.3.2", "dayjs": "^1.11.9", "echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "lodash": "^4.17.21", "mapbox-gl": "^3.13.0", "postprocessing": "7.0.0-alpha-1", "query-string": "^8.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.1", "swagger-ui-dist": "^5.4.2", "three": "^0.160.1", "three-obj-loader": "^1.1.3", "three-stdlib": "^2.29.4", "threebox-plugin": "^2.2.7", "unocss": "^0.51.12"}, "devDependencies": {"@iconify-json/ant-design": "^1.1.5", "@types/mapbox-gl": "^3.4.1", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/three": "^0.178.0", "@unocss/preset-attributify": "^0.51.12", "husky": "^8.0.3", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}