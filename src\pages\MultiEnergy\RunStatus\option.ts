import { px } from '@/utils/util';

let data = [];
const dates = ['00:00', '06:00', '12:00', '18:00', '24:00'];
for (let i = 0; i < dates.length; i++) {
  data.push({
    time: dates[i],
    value: (Math.random() * 50 + 100).toFixed(0),
  });
}
export const option = () => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: px(16),
      },
    },
    legend: {
      data: ['热力产出', '电力产出', '天然气耗量'],
      textStyle: {
        color: '#fff',
        fontSize: px(16),
      },
    },
    grid: {
      left: '1.5%',
      right: '0%',
      bottom: '3%',
      top: '12%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: '#fff',
          fontSize: px(16),
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: 'kW',
      nameTextStyle: {
        color: '#fff',
        fontSize: px(16),
      },
      axisLabel: {
        formatter: '{value}',
        textStyle: {
          color: '#fff',
          fontSize: px(16),
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(176,215,255,0.25)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '热力产出',
        data: data.map((item) => item.value),
        type: 'bar',
        barWidth: px(10),
        itemStyle: {
          borderRadius: [px(10), px(10), 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(23, 117, 233, 1)',
              },
              {
                offset: 0.3,
                color: 'rgba(60, 180, 255, 0.8)',
              },
              {
                offset: 0.7,
                color: 'rgba(100, 200, 255, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(22, 136, 234, 0.102)',
              },
            ],
            global: false,
          },
        },
      },
      {
        name: '电力产出',
        data: data.map((item) => item.value),
        type: 'bar',
        barWidth: px(10),
        itemStyle: {
          borderRadius: [px(10), px(10), 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(60, 221, 235, 1)',
              },
              {
                offset: 0.2,
                color: 'rgba(80, 240, 255, 0.9)',
              },
              {
                offset: 0.5,
                color: 'rgba(120, 200, 255, 0.7)',
              },
              {
                offset: 0.8,
                color: 'rgba(100, 180, 240, 0.4)',
              },
              {
                offset: 1,
                color: 'rgba(22, 136, 234, 0.102)',
              },
            ],
            global: false,
          },
        },
      },
      {
        name: '天然气耗量',
        data: data.map((item) => item.value),
        type: 'bar',
        barWidth: px(10),
        itemStyle: {
          borderRadius: [px(10), px(10), 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(208, 222, 238, 1)',
              },
              {
                offset: 0.25,
                color: 'rgba(220, 235, 250, 0.85)',
              },
              {
                offset: 0.5,
                color: 'rgba(190, 210, 235, 0.7)',
              },
              {
                offset: 0.75,
                color: 'rgba(170, 190, 220, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(208, 222, 238, 0.102)',
              },
            ],
            global: false,
          },
        },
      },
    ],
  };
};
