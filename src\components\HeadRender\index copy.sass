@import '@/utils/helpers.sass'

@keyframes flash
    0%, 100%
        opacity: 1
    50%
        opacity: 0.5
.head
    color: white
    height: px(70)
    width: 100%
    display: flex
    flex-direction: row
    align-items: center
    justify-content: center
    padding: 0 px(20)
    position: relative
.title
    position: relative
    width: 52%

    // margin: 0 auto
    height: px(85)
    text-align: center
    font-family: pangmen
    font-size: px(38)
    letter-spacing: px(8)
    padding-top: px(5)
    padding-right: px(0)
    cursor: pointer
.leftBox,.rightBox
    width: calc( 25% - px(150) )
    display: flex
    flex-direction: row
    transition: all 0.3s ease
    &:hover
        .menuRight, .menuLeft
            opacity: 1
            transform: translateY(0)
.rightBox
    transform: translate(px(-60),0)
.leftBox
    transform: translate(px(80),0)
.menu
    &Right, &Left
        background-size: 100% 100%
        font-size: px(38)
        font-family: 庞门正道标题体
        color: #fff
        width: 100%
        height: px(60)
        line-height: px(60)
        text-align: center
        cursor: pointer
        background-repeat: no-repeat
        transition: all 0.3s ease
        transform: translateY(px(0))
        &:hover
            color: rgb(220, 234, 249)
            text-shadow: 0 0 px(2) rgb(177, 212, 251)
            transform: translateY(px(-2))
            background-position: 0 px(3), 0 0
    &Right
        background-image: url(/header/tab_right.png)
        margin-right: px(-30)
        clip-path: polygon(22% 0, 100% 0, 77% 100%, 0 100%)
        &:hover
            background-image: url(/header/light.png),url(/header/tab_right.png)
    &Left
        background-image: url(/header/tab_left.png)
        margin-left: px(-30)
        clip-path: polygon(0 0, 77% 0, 100% 100%, 23% 100%)
        &:hover
            background-image: url(/header/light.png),url(/header/tab_left.png)
    &RightActive, &LeftActive
        color: rgb(220, 234, 249)
        text-shadow: 0 0 px(2) rgb(177, 212, 251)
        background-position: 0 px(3), 0 0
    &RightActive
        background-image: url(/header/light.png),url(/header/tab_right.png)
    &LeftActive
        background-image: url(/header/light.png),url(/header/tab_left.png)
