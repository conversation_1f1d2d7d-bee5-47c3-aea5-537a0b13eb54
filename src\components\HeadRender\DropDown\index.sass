@import '@/utils/helpers.sass'

.box
    margin-right: px(8)
    width: calc( 100% - px(20) )
    position: absolute
    left: px(20)
.btn
    border: none
    background: transparent
    width: 100%
    font-size: px(16)
    color: rgb(180, 206, 235)
    border-bottom: 2px solid rgb(139, 180, 226)
.btn:hover
    color: rgb(220, 234, 249)
    text-shadow: 0 0 px(2) rgb(177, 212, 251)
    transform: translateY(px(-2)) // add translateY transform on hover
    background-position: 0 px(3), 0 0
    cursor: pointer

.menu
    width: px(50) !important
    border-radius: px(5)
    background: rgba(6, 48, 109,0.9)
ul
    background-color: transparent !important
.menus
    cursor: pointer
    img
        height: px(15)
        width: px(16)
        transform: translateY(px(-2))
