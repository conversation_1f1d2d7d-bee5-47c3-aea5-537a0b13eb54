@import '@/utils/helpers.sass'
.container
  overflow: hidden
  display: flex
  width: px(1920)
  height: calc(100vh - px(180))  // 假设总gap约60px // 改为100vh占满整个视口高度
 
  .left
    flex: 7
    display: flex
    flex-direction: column
    margin-right: px(20)
   
    height: 100%  // 添加高度100%
    .selectContainer
      margin-bottom: px(10)
      display: flex
      align-items: center
      gap: px(10)
      
    .tableContainer
      
  .right
    flex: 3
    display: flex
    flex-direction: column
    gap: px(20)
    min-width: 0
    height: 100%  // 添加高度100%
    .rightTop
      flex: 3  // 改为flex比例，而不是固定高度
      display: flex
      flex-direction: column
      gap: px(20)
      min-height: 0  // 添加最小高度限制
      :global
        .ant-picker
          background: rgba(0, 0, 0, 0.3)
          border: 1px solid rgba(255, 255, 255, 0.3)
          border-radius: px(6)
          width: 100%

        .ant-picker-input > input
          color: #fff !important
          background: transparent

        .ant-picker-input > input::placeholder
          color: rgba(255, 255, 255, 0.6) !important

        .ant-picker-suffix
          color: rgba(255, 255, 255, 0.8)

        .ant-picker:hover
          border-color: rgba(255, 255, 255, 0.6)

        .ant-picker-focused
          border-color: #1890ff
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2)
    .rightBottom
    
      flex: 5  // 改为flex比例，而不是固定高度
      display: flex
      flex-direction: column
       
      min-height: 0  // 添加最小高度限制
      :global
        .ant-picker
          background: rgba(0, 0, 0, 0.3)
          border: 1px solid rgba(255, 255, 255, 0.3)
          border-radius: px(6)
          width: 100%

        .ant-picker-input > input
          color: #fff !important
          background: transparent

        .ant-picker-input > input::placeholder
          color: rgba(255, 255, 255, 0.6) !important

        .ant-picker-suffix
          color: rgba(255, 255, 255, 0.8)

        .ant-picker:hover
          border-color: rgba(255, 255, 255, 0.6)

        .ant-picker-focused
          border-color: #1890ff
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2)

// 其余样式保持不变
.darkTable
  height: 100%
  overflow: hidden
  :global
    .ant-table
      background-color: transparent
      color: white
    .ant-table-thead > tr > th
      background-color: #254B8F
      color: #fff
      border-bottom: px(1) solid rgb(69,99,150)
      padding: px(8)
      text-align: center
      font-size: px(16)
    .ant-table-tbody > tr > td
      border: px(1) solid rgb(69,99,150)
      padding: px(8)
      text-align: center
      font-size: px(16)
      color: #fff
    .ant-table-tbody > tr:nth-child(odd) > td
      background-color: rgb(14,66,128)
    .ant-table-tbody > tr:nth-child(even) > td
      background-color: rgb(26,76,134)
    .ant-table-tbody > tr:hover > td,
    .ant-table-tbody > tr.ant-table-row-hover > td
      background-color: #376FC5 !important
    .ant-pagination-item
      background-color: rgba(0, 0, 0, 0)
      border-color: #5087D8
    .ant-pagination-item-active
      background-color: #376FC5
      border-color: #5087D8
    .ant-pagination-item a
      color: white
    .ant-select-selector
      background-color: rgba(0, 0, 0, 0) !important
      border-color: #5087D8 !important
      color: white
    .ant-select-arrow
      color: white
    .ant-pagination-item-link
      color: white !important
      background-color: #254B8F
      border-color: #5087D8