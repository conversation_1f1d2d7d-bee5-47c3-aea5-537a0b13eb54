import { UNIT_COLORS } from '@/constants/colors';
import { px } from '@/utils/util';

let data = [];
const dates = [
  '02/01',
  '02/02',
  '02/03',
  '02/04',
  '02/05',
  '02/06',
  '02/07',
  '02/08',
  '02/09',
  '02/10',
  '02/11',
  '02/12',
  '02/13',
  '02/14',
  '02/15',
  '02/16',
];

for (let i = 0; i < dates.length; i++) {
  data.push({
    time: dates[i],
    value: (Math.random() * 50 + 100).toFixed(0),
  });
}

function colorRgba(str: string, alpha: number) {
  let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  let sColor = str.toLowerCase();

  if (sColor === 'transparent') return 'transparent';

  if (sColor && reg.test(sColor)) {
    if (sColor.length === 4) {
      let sColorNew = '#';
      for (let i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
      }
      sColor = sColorNew;
    }
    let sColorChange = [];
    for (let i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)));
    }
    return 'rgba(' + sColorChange.join(',') + ',' + alpha + ')';
  }
  return sColor;
}

export const option = () => {
  const barWidth = px(16);
  const barGap = '50%';
  let series = [];

  // 创建单个系列的数据
  series = series.concat([
    {
      data: data.map((item) => item.value),
      type: 'pictorialBar',
      tooltip: {
        show: false,
      },
      barMaxWidth: px(32),
      color: {
        x: 0,
        y: 1,
        x2: 0,
        y2: 0,
        type: 'linear',
        global: false,
        colorStops: [
          {
            offset: 0,
            color: 'transparent',
          },
          {
            offset: 0.2,
            color: colorRgba('#000', 0.2),
          },
          {
            offset: 1,
            color: colorRgba('#000', 0.3),
          },
        ],
      },
      symbolPosition: 'end',
      symbol: 'rect',
      symbolSize: [barWidth / 2, '100%'],
      symbolOffset: [-barWidth / 4, 0],
      zlevel: 1,
    },
    {
      name: '用电量',
      type: 'bar',
      data: data.map((item) => item.value),
      barGap: barGap,
      barWidth: barWidth,
      barMaxWidth: px(32),
      itemStyle: {
        color: {
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          type: 'linear',
          global: false,
          colorStops: [
            {
              offset: 0,
              color: colorRgba(UNIT_COLORS.electricConsumption, 1),
            },
            {
              offset: 0.2,
              color: colorRgba(UNIT_COLORS.electricConsumption, 1),
            },
            {
              offset: 1,
              color: colorRgba(UNIT_COLORS.electricConsumption, 1),
            },
          ],
        },
      },
    },
  ]);

  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(116,176,222,0.3)',
      extraCssText: 'box-shadow: 0 0 8px rgba(0, 128, 255, 0.27) inset;',
      borderWidth: 0,
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(35,49,77,0.01)',
        },
      },
      textStyle: {
        color: '#fff',
        fontSize: px(16),
      },
    },
    grid: {
      top: px(70),
      left: '5%',
      right: '8%',
      bottom: '2%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.time),
      axisLine: {
        show: true,
        lineStyle: {
          color: '#2B72A1',
        },
      },
      axisLabel: {
        fontSize: px(16),
        color: '#fff',
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      interval: 0,
    },
    yAxis: {
      type: 'value',
      name: 'kWh',
      nameTextStyle: {
        align: 'center',
        fontSize: px(16),
        color: '#fff',
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.1)',
        },
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        margin: 10,
        fontSize: px(16),
        color: '#fff',
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#2869A9',
        },
      },
    },
    series,
  };
};
