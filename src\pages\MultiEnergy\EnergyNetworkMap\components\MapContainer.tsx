import mapboxgl from 'mapbox-gl';
import React, { useEffect, useRef } from 'react';

interface MapContainerProps {
  onMapLoad: (map: mapboxgl.Map) => void;
  onMapClick: (e: mapboxgl.MapMouseEvent) => void;
  onMapMouseEnter: (e: mapboxgl.MapMouseEvent) => void;
  onMapMouseLeave: () => void;
  className?: string;
}

/**
 * 地图容器组件
 * 负责地图的初始化和基础事件处理
 */
const MapContainer: React.FC<MapContainerProps> = ({
  onMapLoad,
  onMapClick,
  onMapMouseEnter,
  onMapMouseLeave,
  className,
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    if (!mapContainer.current) return;

    // 初始化地图
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/dark-v11',
      center: [116.3984, 39.9098],
      zoom: 16,
      pitch: 0,
      bearing: 0,
    });

    map.current.on('load', () => {
      if (map.current) {
        onMapLoad(map.current);
      }
    });

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, [onMapLoad]);

  return <div ref={mapContainer} className={className} />;
};

export default MapContainer;
