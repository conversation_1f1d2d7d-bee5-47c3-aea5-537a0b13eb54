import CustomTitle from '@/components/CustomTitle';
import styles from '../index.sass';
export default function () {
  return (
    <div className={styles.middleSection}>
      <CustomTitle>国际碳关税信息滚动展示</CustomTitle>
      <div className={styles.newsContainer}>
        <div className={styles.scrollContent}>
          {/* 第一组新闻 */}
          <div className={styles.newsItem}>
            <div className={styles.newsTime}>2025-03-23 11:37:25</div>
            <div className={styles.newsContent}>
              <span className={styles.newsLevel + ' ' + styles.level1}>
                I 级
              </span>
              <span className={styles.newsText}>
                欧盟碳边境调节机制(CBAM)正式实施，钢铁行业碳关税税率上调至每吨85欧元！
              </span>
            </div>
          </div>

          <div className={styles.newsItem}>
            <div className={styles.newsTime}>2025-03-23 09:37:25</div>
            <div className={styles.newsContent}>
              <span className={styles.newsLevel + ' ' + styles.level2}>
                II 级
              </span>
              <span className={styles.newsText}>
                美国宣布对进口水泥产品征收碳边境税，预计影响全球水泥贸易格局
              </span>
            </div>
          </div>

          <div className={styles.newsItem}>
            <div className={styles.newsTime}>2025-03-22 12:17:25</div>
            <div className={styles.newsContent}>
              <span className={styles.newsLevel + ' ' + styles.level1}>
                I 级
              </span>
              <span className={styles.newsText}>
                英国碳关税政策细则发布，化工产品将纳入征收范围，2026年正式生效！
              </span>
            </div>
          </div>

          <div className={styles.newsItem}>
            <div className={styles.newsTime}>2025-03-21 16:45:12</div>
            <div className={styles.newsContent}>
              <span className={styles.newsLevel + ' ' + styles.level2}>
                II 级
              </span>
              <span className={styles.newsText}>
                加拿大宣布将于2026年实施碳边境调节措施，涵盖钢铁、铝业等重点行业
              </span>
            </div>
          </div>

          {/* 重复第一组新闻以实现无缝循环 */}
          <div className={styles.newsItem}>
            <div className={styles.newsTime}>2025-03-23 11:37:25</div>
            <div className={styles.newsContent}>
              <span className={styles.newsLevel + ' ' + styles.level1}>
                I 级
              </span>
              <span className={styles.newsText}>
                欧盟碳边境调节机制(CBAM)正式实施，钢铁行业碳关税税率上调至每吨85欧元！
              </span>
            </div>
          </div>

          <div className={styles.newsItem}>
            <div className={styles.newsTime}>2025-03-23 09:37:25</div>
            <div className={styles.newsContent}>
              <span className={styles.newsLevel + ' ' + styles.level2}>
                II 级
              </span>
              <span className={styles.newsText}>
                美国宣布对进口水泥产品征收碳边境税，预计影响全球水泥贸易格局
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
