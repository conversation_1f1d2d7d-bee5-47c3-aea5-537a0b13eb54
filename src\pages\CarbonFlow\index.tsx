import CustomSubTitle from '@/components/CustomSubTitle';
import CustomTitle from '@/components/CustomTitle';
import PowerIndicator from '@/components/PowerIndicator';
import ActivePower from './ActivePower';
import CustomMap from './CustomMap';
import styles from './index.sass';
export default function () {
  return (
    <div className={styles.box}>
      <div className={styles.left}>
        <CustomTitle>碳流可视化</CustomTitle>
        <div className={styles.map}>
          <CustomMap></CustomMap>
        </div>
      </div>
      <div className={styles.rightSection}>
        <CustomTitle>实时数据统计</CustomTitle>
        <div className={styles.indicateContainer}>
          <CustomSubTitle className={styles.subtitle}>
            碳势信息统计
          </CustomSubTitle>
          <div className={styles.indicate}>
            <PowerIndicator value={180} unit="t" label="最高碳浓度" />
            <PowerIndicator value={65} unit="t" label="最低碳浓度" />
            <PowerIndicator value={80} unit="t" label="日均碳浓度" />
          </div>
        </div>
        <div className={styles.chartsContainer}>
          <CustomSubTitle>园区碳势变化趋势</CustomSubTitle>
          {/* <CustomSelectV2
            className={styles.select}
            style={{ width: px(100), height: px(30), marginTop: px(10) }}
            options={[
              { label: '日', value: 'day' },
              { label: '月', value: 'month' },
              { label: '年', value: 'year' },
            ]}
            onChange={(value) => {
              console.log(value);
            }}
          /> */}
          <div className={`${styles.chartRow} ${styles.activePower}`}>
            <div className={styles.labelContainer}>
              <div className={styles.labelBar}></div>
              <div className={styles.label}>碳浓度</div>
            </div>
            <div className={styles.chartContent}>
              <ActivePower />
            </div>
          </div>
          <div className={`${styles.chartRow} ${styles.reactivePower}`}>
            <div className={styles.labelContainer}>
              <div className={styles.labelBar}></div>
              <div className={styles.label}>碳强度</div>
            </div>
            <div className={styles.chartContent}>
              <ActivePower />
            </div>
          </div>
          <div className={`${styles.chartRow} ${styles.solarPower}`}>
            <div className={styles.labelContainer}>
              <div className={styles.labelBar}></div>
              <div className={styles.label}>碳排放速率</div>
            </div>
            <div className={styles.chartContent}>
              <ActivePower />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
