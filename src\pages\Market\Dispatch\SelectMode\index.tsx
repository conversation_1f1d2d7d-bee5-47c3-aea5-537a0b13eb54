import CustomTipModal from '@/components/CustomTipModal';
import React, { useState } from 'react';
import styles from './index.sass';
interface Props {
  name?: string;
}
type Mode = {
  isMarket: boolean;
};
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  // Local state for display purposes only
  const [scheduleMode, setScheduleMode] = useState<Mode>({ isMarket: true });
  const [open, setOpen] = useState(false);

  const selectMode = (selected: Mode) => {
    if (selected.isMarket !== scheduleMode.isMarket) {
      setOpen(true);
    }
  };

  const handleOk = () => {
    // For display purposes only - no API call
    setScheduleMode({ isMarket: !scheduleMode.isMarket });
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
  };
  return (
    <div className={styles.box}>
      <div className={styles.title}>调度模式选择</div>
      <div className={styles.items}>
        <div
          className={`${styles.market} ${
            scheduleMode.isMarket && styles.selected
          }`}
          onClick={() => selectMode({ isMarket: true })}
        >
          市场调度模式
        </div>
        <div
          className={`${styles.cost} ${
            !scheduleMode.isMarket && styles.selected
          }`}
          onClick={() => selectMode({ isMarket: false })}
        >
          成本调度模式
        </div>
      </div>
      <CustomTipModal
        title="提示"
        open={open}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
      >
        确认切换为
        {scheduleMode.isMarket
          ? '成本调度模式？切换后将在明日生效，成本调度模式下，平台将根据内部所有资源点实际发用电情况进行调度，无法参与市场交易。'
          : '市场调度模式？切换后将在明日生效，市场调度模式下，平台会根据您参与市场交易情况对资源点进行调度。'}
      </CustomTipModal>
    </div>
  );
};

export default Index;
