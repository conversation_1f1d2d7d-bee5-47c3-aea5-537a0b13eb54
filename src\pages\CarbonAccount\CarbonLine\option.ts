import { px } from '@/utils/util';
const xd = [];
const yd1 = [];
const yd2 = [];
for (let i = 0; i < 24; i++) {
  xd.push(i + ':00');
  yd1.push(Math.random() * 20 + 20);
  yd2.push(Math.random() * 20 + 20);
}
export const option = () => {
  return {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: true,
      textStyle: {
        color: 'white',
        fontSize: px(16),
      },
      top: px(20),
    },
    grid: {
      top: px(80),
      left: px(20),
      right: px(20),
      bottom: px(30),
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        fontSize: px(16),
        color: '#fff',
      },
      data: xd,
    },
    yAxis: [
      {
        type: 'value',
        nameTextStyle: {
          fontSize: px(16),
          color: '#fff',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
          },
        },
        axisLabel: {
          fontSize: px(16),
          color: '#fff',
          formatter: function (value) {
            if (Number.isInteger(value)) {
              return value.toString();
            } else {
              return value.toFixed(2);
            }
          },
        },
      },
    ],
    series: [
      {
        name: '碳浓度',
        type: 'line',
        symbol: 'none',
        showAllSymbol: false,
        symbolSize: 0,
        smooth: true,
        lineStyle: {
          normal: {
            color: '#F4CF62',
            width: px(4),
          },
        },
        data: yd1,
      },
      {
        name: '碳排放量',
        type: 'line',
        symbol: 'none',
        showAllSymbol: false,
        symbolSize: 0,
        smooth: true,
        lineStyle: {
          normal: {
            color: '#0A8EEE',
            width: px(4),
          },
        },
        data: yd2,
      },
    ],
  };
};
