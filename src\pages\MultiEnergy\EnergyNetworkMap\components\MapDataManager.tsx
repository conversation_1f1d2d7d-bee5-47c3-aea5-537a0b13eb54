import { useEffect, useState } from 'react';
import { PipelineData, FacilityData, RealTimeData } from './types';

interface MapDataManagerProps {
  onDataUpdate: (data: RealTimeData) => void;
}

/**
 * 地图数据管理组件
 * 负责数据的初始化、实时更新和状态管理
 */
const MapDataManager: React.FC<MapDataManagerProps> = ({ onDataUpdate }) => {
  // 模拟管道数据
  const [initialPipelineData] = useState<PipelineData[]>([
    // 热网主管道
    {
      id: 'heat_main_1',
      type: 'heat',
      coordinates: [
        [116.3974, 39.9093],
        [116.3984, 39.9103],
        [116.3994, 39.9113],
        [116.4004, 39.9123],
      ],
      flow: 45.2,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'heat_main_2',
      type: 'heat',
      coordinates: [
        [116.3954, 39.9073],
        [116.3964, 39.9083],
        [116.3974, 39.9093],
        [116.3984, 39.9103],
      ],
      flow: 38.7,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'heat_branch_1',
      type: 'heat',
      coordinates: [
        [116.3984, 39.9103],
        [116.3994, 39.9093],
        [116.4004, 39.9083],
      ],
      flow: 18.5,
      direction: 'backward',
      status: 'warning',
      faultInfo: {
        level: 2,
        title: '二级响应',
        occurTime: '2024-01-15 14:30:25',
        description: '管道温度偏高，超出正常范围5℃',
        location: '热网支线1号-管段HB001',
        currentStatus: '处理中',
      },
    },
    {
      id: 'heat_branch_2',
      type: 'heat',
      coordinates: [
        [116.3994, 39.9113],
        [116.4014, 39.9103],
        [116.4024, 39.9093],
      ],
      flow: 22.3,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'heat_branch_3',
      type: 'heat',
      coordinates: [
        [116.3964, 39.9083],
        [116.3954, 39.9093],
        [116.3944, 39.9103],
      ],
      flow: 0,
      direction: 'forward',
      status: 'error',
      faultInfo: {
        level: 1,
        title: '一级响应',
        occurTime: '2024-01-15 16:45:12',
        description: '管道流量中断，疑似管道破裂',
        location: '热网支线3号-管段HB003',
        currentStatus: '待处理',
      },
    },
    // 气网主管道
    {
      id: 'gas_main_1',
      type: 'gas',
      coordinates: [
        [116.3964, 39.9083],
        [116.3974, 39.9093],
        [116.3984, 39.9103],
        [116.3994, 39.9113],
      ],
      flow: 32.8,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'gas_main_2',
      type: 'gas',
      coordinates: [
        [116.3944, 39.9063],
        [116.3954, 39.9073],
        [116.3964, 39.9083],
        [116.3974, 39.9093],
      ],
      flow: 28.4,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'gas_branch_1',
      type: 'gas',
      coordinates: [
        [116.3974, 39.9093],
        [116.3984, 39.9083],
        [116.3994, 39.9073],
      ],
      flow: 15.6,
      direction: 'forward',
      status: 'warning',
      faultInfo: {
        level: 3,
        title: '三级响应',
        occurTime: '2024-01-15 13:20:08',
        description: '管道压力轻微波动，在安全范围内',
        location: '气网支线1号-管段GB001',
        currentStatus: '处理中',
      },
    },
    {
      id: 'gas_branch_2',
      type: 'gas',
      coordinates: [
        [116.3984, 39.9103],
        [116.4004, 39.9113],
        [116.4014, 39.9123],
      ],
      flow: 19.2,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'gas_branch_3',
      type: 'gas',
      coordinates: [
        [116.3954, 39.9073],
        [116.3944, 39.9083],
        [116.3934, 39.9093],
      ],
      flow: 12.8,
      direction: 'backward',
      status: 'normal',
    },
  ]);

  // 模拟设施数据
  const [initialFacilityData] = useState<FacilityData[]>([
    // 发电设施
    {
      id: 'power_plant_1',
      type: 'power_plant',
      coordinates: [116.3974, 39.9093],
      name: '热电联产电厂1号',
      status: 'running',
      capacity: 100,
      currentOutput: 85,
    },
    {
      id: 'power_plant_2',
      type: 'power_plant',
      coordinates: [116.3954, 39.9073],
      name: '热电联产电厂2号',
      status: 'error',
      capacity: 80,
      currentOutput: 0,
      faultInfo: {
        level: 1,
        title: '一级响应',
        occurTime: '2024-01-15 15:22:35',
        description: '主机组故障停机，安全系统启动',
        location: '电厂2号-主机组MG002',
        currentStatus: '处理中',
      },
    },
    // 变电设施
    {
      id: 'substation_1',
      type: 'substation',
      coordinates: [116.3994, 39.9113],
      name: '变电站1号',
      status: 'running',
    },
    {
      id: 'substation_2',
      type: 'substation',
      coordinates: [116.4014, 39.9123],
      name: '变电站2号',
      status: 'maintenance',
    },
    // 储能设施
    {
      id: 'storage_1',
      type: 'storage',
      coordinates: [116.4004, 39.9083],
      name: '储能站1号',
      status: 'maintenance',
    },
    {
      id: 'storage_2',
      type: 'storage',
      coordinates: [116.3944, 39.9063],
      name: '储能站2号',
      status: 'running',
      capacity: 50,
      currentOutput: 25,
    },
    // 热用户
    {
      id: 'heat_user_1',
      type: 'heat_user',
      coordinates: [116.4004, 39.9123],
      name: '办公楼A栋',
      status: 'running',
      capacity: 15,
      currentOutput: 12,
    },
    {
      id: 'heat_user_2',
      type: 'heat_user',
      coordinates: [116.4024, 39.9093],
      name: '住宅区1号',
      status: 'running',
      capacity: 25,
      currentOutput: 18,
    },
    {
      id: 'heat_user_3',
      type: 'heat_user',
      coordinates: [116.3944, 39.9103],
      name: '商业中心',
      status: 'error',
      capacity: 30,
      currentOutput: 0,
      faultInfo: {
        level: 2,
        title: '二级响应',
        occurTime: '2024-01-15 12:15:42',
        description: '供热系统循环泵故障，影响供热效果',
        location: '商业中心-循环泵CP001',
        currentStatus: '待处理',
      },
    },
    {
      id: 'heat_user_4',
      type: 'heat_user',
      coordinates: [116.3934, 39.9093],
      name: '工厂厂房1号',
      status: 'running',
      capacity: 40,
      currentOutput: 35,
    },
    // 气用户
    {
      id: 'gas_user_1',
      type: 'gas_user',
      coordinates: [116.3994, 39.9073],
      name: '食堂燃气站',
      status: 'running',
      capacity: 8,
      currentOutput: 6,
    },
    {
      id: 'gas_user_2',
      type: 'gas_user',
      coordinates: [116.4014, 39.9103],
      name: '锅炉房1号',
      status: 'running',
      capacity: 20,
      currentOutput: 16,
    },
    {
      id: 'gas_user_3',
      type: 'gas_user',
      coordinates: [116.3944, 39.9083],
      name: '实验楼燃气站',
      status: 'warning',
      capacity: 12,
      currentOutput: 8,
      faultInfo: {
        level: 3,
        title: '三级响应',
        occurTime: '2024-01-15 11:45:18',
        description: '燃气表读数异常，需要校准',
        location: '实验楼-燃气表GM003',
        currentStatus: '处理中',
      },
    },
    // 工业用户
    {
      id: 'industrial_user_1',
      type: 'industrial_user',
      coordinates: [116.4024, 39.9113],
      name: '生产车间A',
      status: 'running',
      capacity: 60,
      currentOutput: 45,
    },
    {
      id: 'industrial_user_2',
      type: 'industrial_user',
      coordinates: [116.3924, 39.9083],
      name: '生产车间B',
      status: 'stopped',
      capacity: 50,
      currentOutput: 0,
    },
    {
      id: 'industrial_user_3',
      type: 'industrial_user',
      coordinates: [116.4034, 39.9103],
      name: '化工装置1号',
      status: 'error',
      capacity: 80,
      currentOutput: 0,
      faultInfo: {
        level: 1,
        title: '一级响应',
        occurTime: '2024-01-15 09:30:15',
        description: '反应器温度过高，紧急停机',
        location: '化工装置1号-反应器R001',
        currentStatus: '处理中',
      },
    },
  ]);

  // 实时数据状态
  const [realTimeData, setRealTimeData] = useState<RealTimeData>({
    pipelines: initialPipelineData,
    facilities: initialFacilityData,
  });

  // 实时数据更新
  useEffect(() => {
    const updateInterval = setInterval(() => {
      // 模拟实时数据更新
      const updatedPipelines = realTimeData.pipelines.map((pipeline) => ({
        ...pipeline,
        flow: Math.max(0, pipeline.flow + (Math.random() - 0.5) * 5), // 随机波动，确保非负
        status: Math.random() > 0.95 ? 'warning' : pipeline.status, // 偶尔出现警告
      }));

      const updatedFacilities = realTimeData.facilities.map((facility) => ({
        ...facility,
        currentOutput: facility.currentOutput
          ? Math.max(0, facility.currentOutput + (Math.random() - 0.5) * 10)
          : undefined,
        status: Math.random() > 0.98 ? 'error' : facility.status, // 偶尔出现故障
      }));

      const newData = {
        pipelines: updatedPipelines,
        facilities: updatedFacilities,
      };

      setRealTimeData(newData);
      onDataUpdate(newData);
    }, 3000); // 每3秒更新一次

    return () => clearInterval(updateInterval);
  }, [realTimeData, onDataUpdate]);

  // 初始数据传递
  useEffect(() => {
    onDataUpdate(realTimeData);
  }, []);

  return null; // 这是一个逻辑组件，不渲染任何UI
};

export default MapDataManager;
