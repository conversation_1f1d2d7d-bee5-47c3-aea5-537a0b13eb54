import { px } from '@/utils/util';
import * as echarts from 'echarts';
let data = [
  {
    name: '储氢罐',
    value: 320,
  },
  {
    name: '储气罐',
    value: 720,
  },
  {
    name: '储热罐',
    value: 1450,
  },
];
let xAxisData = [];
let seriesData1 = [];
let barTopColor = ['#02c3f1', '#53e568', '#a154e9'];
let barBottomColor = [
  'rgba(2,195,241,0.1)',
  'rgba(83, 229, 104, 0.1)',
  'rgba(161, 84, 233, 0.1)',
];
data.forEach((item) => {
  xAxisData.push(item.name);
  seriesData1.push(item.value);
});
export const option = () => {
  return {
    grid: {
      top: '10%',
      bottom: '22%',
      left: '10%',
      right: '10%',
    },
    xAxis: {
      data: xAxisData,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        margin: 25,
        align: 'center',
        formatter: function (params) {
          return '{a|' + '\n' + params + '}';
        },
        textStyle: {
          fontSize: px(16),
          color: '#ffffff',
          rich: {
            a: {
              fontSize: px(16),
              color: '#ffffff',
            },
            b: {
              height: 20,
              fontSize: px(16),
              color: '#ffffff',
            },
          },
        },
      },
      interval: 0,
    },
    yAxis: {
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    series: [
      {
        name: '柱顶部',
        type: 'pictorialBar',
        symbolSize: [26, 10],
        symbolOffset: [0, -5],
        z: 12,
        itemStyle: {
          normal: {
            color: function (params) {
              return barTopColor[params.dataIndex];
            },
          },
        },
        label: {
          show: true,
          position: 'top',
          fontSize: px(16),
          color: '#fff',
        },
        symbolPosition: 'end',
        data: seriesData1,
      },
      {
        name: '柱底部',
        type: 'effectScatter',
        symbolSize: [26, 10],
        symbolOffset: [0, 10],
        z: 12,
        itemStyle: {
          normal: {
            color: function (params) {
              return barTopColor[params.dataIndex];
            },
          },
        },
        data: [3, 3, 3],
      },
      {
        name: '第一圈',
        type: 'pictorialBar',
        symbolSize: [46, 16],
        symbolOffset: [0, 11],
        z: 11,
        itemStyle: {
          normal: {
            color: 'transparent',
            borderColor: '#3ACDC5',
            borderWidth: 2,
          },
        },
        data: seriesData1,
      },
      {
        name: '第二圈',
        type: 'pictorialBar',
        symbolSize: [62, 22],
        symbolOffset: [0, 17],
        z: 10,
        itemStyle: {
          normal: {
            color: 'transparent',
            borderColor: barTopColor[0],
            borderWidth: 2,
          },
        },
        data: seriesData1,
      },
      {
        type: 'bar',
        itemStyle: {
          normal: {
            color: function (params) {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 1,
                  color: barTopColor[params.dataIndex],
                },
                {
                  offset: 0,
                  color: barBottomColor[params.dataIndex],
                },
              ]);
            },
            opacity: 0.8,
          },
        },
        z: 16,
        silent: true,
        barWidth: 26,
        barGap: '-100%', // Make series be overlap
        data: seriesData1,
      },
    ],
  };
};
