import CustomSelect from '@/components/CustomSelect';
import { px } from '@/utils/util';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import React from 'react';
import styles from './index.sass';
import { mockData, mockUnit } from './mock';
import getOption from './option';

const Index: React.FC = () => {
  const container = React.createRef<HTMLDivElement>();
  const myChart: any = React.createRef();

  React.useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }
    const option = getOption(mockData, mockUnit);
    myChart.current.setOption(option, true);
  }, []);

  return (
    <div className={styles.infoChartContainer}>
      <div className={styles.top}>
        <div className={styles.selectContainer}>
          <CustomSelect
            style={{ width: px(150) }}
            options={[
              { value: '年度', label: '年度' },
              { value: '月度', label: '月度' },
            ]}
          />
        </div>
        <div className={styles.datePickerContainer}>
          <DatePicker.RangePicker
            value={[dayjs('2025-02-01'), dayjs('2025-02-26')]}
          />
        </div>
      </div>
      <div className={styles.chartContainer} ref={container}></div>
    </div>
  );
};

export default Index;
