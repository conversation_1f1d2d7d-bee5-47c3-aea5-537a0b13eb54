// src/components/MyModel.tsx

import { useGLTF } from '@react-three/drei';
import React, { useEffect, useState } from 'react';
import * as THREE from 'three';
import Label2d from '../Label2d';

// 定义模型子对象的信息接口 (保持不变)
export interface IndividualModelInfo {
  name: string;
  uuid: string;
  position: THREE.Vector3;
  rotation: THREE.Euler;
  scale: THREE.Vector3;
  displayText1: string;
  displayText2: string;
  boundingBox?: THREE.Box3;
}

interface MyModelProps {
  onScrewMachineLoad?: (info: IndividualModelInfo[]) => void; // 用于向父组件传递每个子模型信息的回调
}

const MyModel: React.FC<MyModelProps> = ({ onScrewMachineLoad }) => {
  const gltf = useGLTF('/model/宁波项目场景.glb', true);
  // 得到所有螺杆机，并增加标签
  const [allModel, setAllModel] = useState([]);
  useEffect(() => {
    if (gltf && gltf.scene) {
      const individualObjectsInfo: IndividualModelInfo[] = [];
      const mod = [];
      // 遍历模型中的所有对象
      onScrewMachineLoad?.(individualObjectsInfo);
      gltf.scene.traverse((obj) => {
        if (obj.name.includes('螺杆')) {
          mod.push(obj);
          setAllModel(mod);
        }
      });
    }
  }, [gltf]);

  return (
    <>
      <primitive object={gltf.scene} scale={1} />
      {allModel.map((mod) => {
        return (
          <Label2d
            key={mod.uuid}
            position={mod.position}
            name={mod.name}
          ></Label2d>
        );
      })}
    </>
  );
};

export default MyModel;
