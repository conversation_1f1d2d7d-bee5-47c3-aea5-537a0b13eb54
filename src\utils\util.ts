import { TimeRangePickerProps } from 'antd';
import locale from 'antd/lib/date-picker/locale/zh_CN';
import dayjs from 'dayjs';
const { clientWidth, clientHeight } = document.documentElement;

const targetRatio = 1920 / 1080;
const pageWidth =
  clientWidth / clientHeight > targetRatio
    ? clientHeight * targetRatio
    : clientWidth;
const pageHeight = pageWidth / targetRatio;
window.pageWidth = pageWidth;
window.pageHeight = pageHeight;
document.documentElement.style.fontSize = pageWidth / 100 + 'px';
locale!.lang = {
  ...locale!.lang,
  monthFormat: 'M月',
  shortWeekDays: ['日', '一', '二', '三', '四', '五', '六'],
};
export const myLocale = locale;
export const px = (n: number) => (n / 1920) * window.pageWidth;
// 全局颜色变量
export const bgColor = '#0D2C5C'; // 浅灰蓝色背景，更专业且与深蓝色UI元素形成良好对比
export const routerMap = [
  {
    name: '电力计量',
    path: '/home/<USER>',
  },
  {
    name: '多能流信息',
    path: '/home/<USER>',
  },
  {
    name: '园区碳流',
    path: '/home/<USER>',
  },
  {
    name: '碳计量信息',
    path: '/home/<USER>',
  },
  {
    name: '全景碳看板',
    path: '/home/<USER>',
  },
];
export const datetimeFormat = 'YYYY-MM-DD HH:mm:ss';
export const dateHourFormat = 'YYYY-MM-DD HH';
export const dateMinuteFormat = 'YYYY-MM-DD HH:mm';
export const dateFormat = 'YYYY-MM-DD';
export const defalutTime = [dayjs().add(-1, 'd'), dayjs().add(0, 'd')];
export const defalutTimeString = [
  defalutTime[0].format(datetimeFormat),
  defalutTime[1].format(datetimeFormat),
];
export const defaultSearch = {
  time: defalutTimeString,
  resource: '',
  regulate: '可调容量',
  date: defalutTimeString[1],
};
export const rangePresets: TimeRangePickerProps['presets'] = [
  { label: '7天前', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '14天前', value: [dayjs().add(-14, 'd'), dayjs()] },
  { label: '30天前', value: [dayjs().add(-30, 'd'), dayjs()] },
  { label: '90天前', value: [dayjs().add(-90, 'd'), dayjs()] },
];
