import Tab from '@/components/Tab';
import { ITabOptions } from '@/components/Tab/typing';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';
import styles from './index.sass';
import { getOption } from './option';

interface Props {
  name?: string;
}

const Index: React.FC<Props> = (props: Props) => {
  const defalutTime = [dayjs().add(-7, 'd'), dayjs().add(0, 'd')];
  const {} = props;

  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  // Static data
  const staticData = {
    countList: [12, 17, 16, 13, 12, 16, 13, 12, 15, 14],
    resourceNameList: [
      '储氢-003',
      '储室-002',
      '储氢-001',
      '储热罐003',
      '储热罐-002',
      '储热耀001',
      '电储能004',
      '电储能-003',
      '电储能-002',
      '电储能001',
    ],
  };
  useEffect(() => {
    if (container.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
      myChart.current.setOption(
        getOption(staticData.resourceNameList, staticData.countList),
      );
    }
  }, []);

  const onSearch = (_value: ITabOptions) => {
    console.log(_value);
    // Using static data, no need for API call
    if (myChart.current) {
      myChart.current.setOption(
        getOption(staticData.resourceNameList, staticData.countList),
      );
    }
  };
  return (
    <>
      <Tab
        title="各储能设备累计调度次数"
        optionTypes={['date']}
        onSearch={onSearch}
        defaultTimeRange={defalutTime}
      ></Tab>
      <div className={styles.box} ref={container}></div>
    </>
  );
};

export default Index;
