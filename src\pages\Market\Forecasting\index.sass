@import '@/utils/helpers.sass'
.box
    width: 100%
    height: 100%
    display: flex
    flex-direction: column
    overflow: hidden
.top
    height: 50%
    width: 100%
    display: flex
    justify-content: space-between
.bottom
    height: calc( 50% - px(16) )
    margin-top: px(16)
    width: 100%
.item1,.item2,.item3
    background-image: url(/pane5.png)
    background-size: 100% 100%
    background-repeat: no-repeat
    padding: px(15)
    height: 100%
    position: relative
    img
        width: px(35)
        height: px(35)
        margin-right: px(10)
.item1,.item2
    width: calc( 50% - px(8) )
.item3
    background-image: url(/pane6.png)
    width: 100%
.title
    width: 100%
    text-align: center
    font-family: youshe
    font-size: px(24)
    letter-spacing: px(4)
    padding-top: px(0)
.time
    display: flex
    margin-bottom: px(16)
    align-items: center
.button
    margin-left: px(16)
