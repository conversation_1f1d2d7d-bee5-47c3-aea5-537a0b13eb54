import { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { OutputPass } from 'three/examples/jsm/postprocessing/OutputPass';

import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import {
  CSS2DObject,
  CSS2DRenderer,
} from 'three/examples/jsm/renderers/CSS2DRenderer';
import { CSS3DRenderer } from 'three/examples/jsm/renderers/CSS3DRenderer';

import styles from './index.sass';
import { label2d } from './util';
export default function () {
  const mountRef = useRef<HTMLDivElement>(null);
  // 使用 ref 存储动画 ID
  const animationFrameRef = useRef<number | null>(null);
  useEffect(() => {
    const container = mountRef.current!;
    let w = container.clientWidth;
    let h = container.clientHeight;
    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color('#233044');
    const camera = new THREE.PerspectiveCamera(75, w / h, 0.1, 1000);
    camera.position.set(2, 3, 2);
    camera.lookAt(0, 0, 0); // 确保相机正对原点

    const renderer = new THREE.WebGLRenderer({
      antialias: true,
    });
    // renderer.setClearColor('#233044'); // 设置为白色
    renderer.setSize(w, h);
    container.appendChild(renderer.domElement);
    // 创建CSSRenderer并添加到DOM
    const labelRenderer3d = new CSS3DRenderer();
    labelRenderer3d.setSize(w, h);
    labelRenderer3d.domElement.style.position = 'absolute';
    labelRenderer3d.domElement.style.top = '0';
    labelRenderer3d.domElement.style.pointerEvents = 'none'; // 关键：禁用鼠标事件
    const labelRenderer2d = new CSS2DRenderer();
    labelRenderer2d.setSize(w, h);
    labelRenderer2d.domElement.style.position = 'absolute';
    labelRenderer2d.domElement.style.top = '0';
    labelRenderer2d.domElement.style.pointerEvents = 'none'; // 关键：禁用鼠标事件
    container.appendChild(labelRenderer3d.domElement);
    container.appendChild(labelRenderer2d.domElement);

    // // 创建坐标系辅助线（参数为轴线长度）
    // const axesHelper = new THREE.AxesHelper(5);
    // // 添加到场景
    // scene.add(axesHelper);
    // 添加控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;

    // 添加灯光
    const ambientLight = new THREE.AmbientLight('#fff', 1);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(0, 30, 0);
    scene.add(directionalLight);

    // 添加高亮效果
    const composer = new EffectComposer(renderer);
    const renderPass = new RenderPass(scene, camera);
    renderPass.clear = true;
    renderPass.clearDepth = true;
    composer.addPass(renderPass);
    const outlinePass = new OutlinePass(new THREE.Vector2(w, h), scene, camera);
    outlinePass.edgeStrength = 3.0; // 轮廓强度
    outlinePass.edgeGlow = 0.5; // 发光强度
    outlinePass.edgeThickness = 1.0; // 轮廓粗细
    outlinePass.pulsePeriod = 2.0; // 闪烁周期（设为0则不闪烁）
    outlinePass.visibleEdgeColor.set(0x00ffff); // 轮廓颜色
    composer.addPass(outlinePass);

    // 加载模型
    const loader = new GLTFLoader();
    loader.load('/model/宁波项目场景.glb', (gltf) => {
      // 得到所有房屋的位置，并且在每个房屋的上面添加标注
      for (let child of gltf.scene.children) {
        if (child.name.includes('螺')) {
          // 添加标签
          let label = new CSS2DObject(label2d(child.name));
          label.position.set(0, 0, 0);
          child.add(label);
        }
      }
      scene.add(gltf.scene);
    });

    // 修改鼠标移动事件
    // 射线检测相关变量
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();
    const rect = container.getBoundingClientRect();

    container.addEventListener('mousemove', (event) => {
      mouse.x = ((event.clientX - rect.left) / w) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / h) * 2 + 1;
      raycaster.setFromCamera(mouse, camera);
      const intersects = raycaster.intersectObjects(scene.children);
      //获取第一个对象并高亮
      let obj = intersects?.[0]?.object;
      outlinePass.selectedObjects = obj?.name?.includes('房屋')
        ? [intersects[0].object]
        : [];
    });
    // 关键：必须添加 OutputPass 恢复色调映射
    const outputPass = new OutputPass();
    composer.addPass(outputPass);
    // 动画循环
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);
      composer.render(); // 使用后期处理器渲染
      // renderer.render(scene, camera);
      labelRenderer3d.render(scene, camera);
      labelRenderer2d.render(scene, camera);
      controls.update();
    };
    animate();

    // 响应式处理
    const handleResize = () => {
      camera.aspect = w / h;
      camera.updateProjectionMatrix();
      renderer.setSize(w, h);
    };
    window.addEventListener('resize', handleResize);

    // 清理
    return () => {
      window.removeEventListener('resize', handleResize);
      container.removeChild(renderer.domElement);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return <div ref={mountRef} className={styles.box} />;
}
