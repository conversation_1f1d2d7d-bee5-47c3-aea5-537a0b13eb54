import CustomSelect from '@/components/CustomSelect';
import CustomTable from '@/components/CustomTable';
import CustomTitle from '@/components/CustomTitle';
import { px } from '@/utils/util';
import { Button } from 'antd';
import { useState } from 'react';
import ElectricLine from './ElectricLine';
import styles from './index.sass';
import InfoChart from './InfoChart';

export default function () {
  // 图片中的电站数据
  const stationData = [
    {
      key: '1',
      name: '宁波光伏电站A区',
      type: '光伏发电',
      capacity: 50,
      annualPower: 5800,
      annualProfit: 4930,
    },
    {
      key: '2',
      name: '宁波光伏电站B区',
      type: '光伏发电',
      capacity: 45,
      annualPower: 5200,
      annualProfit: 4420,
    },
    {
      key: '3',
      name: '北仑风电场一期',
      type: '风力发电',
      capacity: 80,
      annualPower: 18200,
      annualProfit: 11284,
    },
    {
      key: '4',
      name: '象山风电场二期',
      type: '风力发电',
      capacity: 60,
      annualPower: 13600,
      annualProfit: 8432,
    },
    {
      key: '5',
      name: '宁波储能中心',
      type: '储能电站',
      capacity: 30,
      annualPower: 2400,
      annualProfit: 2880,
    },
    {
      key: '6',
      name: '宁波钢铁余热发电',
      type: '工业余热',
      capacity: 20,
      annualPower: 4500,
      annualProfit: 3510,
    },
    {
      key: '7',
      name: '宁波石化余热发电',
      type: '工业余热',
      capacity: 15,
      annualPower: 3400,
      annualProfit: 2652,
    },
    {
      key: '8',
      name: '宁波生物质电厂',
      type: '生物质发电',
      capacity: 15,
      annualPower: 3200,
      annualProfit: 3040,
    },
    {
      key: '9',
      name: '宁波垃圾焚烧发电',
      type: '垃圾发电',
      capacity: 25,
      annualPower: 4800,
      annualProfit: 3840,
    },
    {
      key: '10',
      name: '宁波潮汐电站',
      type: '潮汐发电',
      capacity: 10,
      annualPower: 1200,
      annualProfit: 1080,
    },
    {
      key: '11',
      name: '宁波光伏电站C区',
      type: '光伏发电',
      capacity: 55,
      annualPower: 6400,
      annualProfit: 5440,
    },
    {
      key: '12',
      name: '宁波光伏电站D区',
      type: '光伏发电',
      capacity: 40,
      annualPower: 4600,
      annualProfit: 3910,
    },
    {
      key: '13',
      name: '慈溪风电场',
      type: '风力发电',
      capacity: 70,
      annualPower: 15800,
      annualProfit: 9796,
    },
    {
      key: '14',
      name: '宁波储能试点区',
      type: '储能电站',
      capacity: 25,
      annualPower: 2000,
      annualProfit: 2400,
    },
    {
      key: '15',
      name: '宁波化工余热发电',
      type: '工业余热',
      capacity: 18,
      annualPower: 3800,
      annualProfit: 2964,
    },
  ];

  // 状态管理
  const [selectedResourceName, setSelectedResourceName] = useState<
    string | undefined
  >(undefined);
  const [selectedResourceType, setSelectedResourceType] = useState<
    string | undefined
  >(undefined);

  // 生成资源名称选项
  const resourceNameOptions = [
    { value: undefined, label: '全部资源' },
    ...Array.from(new Set(stationData.map((item) => item.name))).map(
      (name) => ({
        value: name,
        label: name,
      }),
    ),
  ];

  // 生成资源类型选项
  const resourceTypeOptions = [
    { value: undefined, label: '全部类型' },
    ...Array.from(new Set(stationData.map((item) => item.type))).map(
      (type) => ({
        value: type,
        label: type,
      }),
    ),
  ];

  const columns = [
    { title: '序号', dataIndex: 'key', key: 'key', width: 60 },
    { title: '资源名称', dataIndex: 'name', key: 'name', width: 180 },
    { title: '资源类型', dataIndex: 'type', key: 'type', width: 120 },
    {
      title: '装机容量(MW)',
      dataIndex: 'capacity',
      key: 'capacity',
      width: 120,
    },
    {
      title: '年发电量(万kWh)',
      dataIndex: 'annualPower',
      key: 'annualPower',
      width: 140,
    },
    {
      title: '年收益(万元)',
      dataIndex: 'annualProfit',
      key: 'annualProfit',
      width: 120,
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.left}>
        <div className={styles.selectContainer}>
          资源名称：
          <CustomSelect
            style={{ width: px(150) }}
            placeholder="选择资源名称"
            value={selectedResourceName}
            onChange={setSelectedResourceName}
            options={resourceNameOptions}
          />
          资源类型：
          <CustomSelect
            style={{ width: px(150) }}
            placeholder="选择资源类型"
            value={selectedResourceType}
            onChange={setSelectedResourceType}
            options={resourceTypeOptions}
          />
          <Button className={styles.button} type="primary">
            查询
          </Button>
        </div>
        <div className={styles.tableContainer}>
          <CustomTable
            columns={columns}
            dataSource={stationData}
            pagination={{
              pageSize: 15,
              showSizeChanger: false,

              position: ['bottomCenter'],
            }}
          />
        </div>
      </div>

      <div className={styles.right}>
        <div className={styles.rightTop}>
          <CustomTitle>多能源收益占比</CustomTitle>
          <InfoChart />
        </div>
        <div className={styles.rightBottom}>
          <CustomTitle>碳配额交易信息</CustomTitle>
          <div style={{ height: px(900) }}>
            <ElectricLine />
          </div>
        </div>
      </div>
    </div>
  );
}
