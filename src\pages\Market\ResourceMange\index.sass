@import '@/utils/helpers.sass'
.box
    width: 100%
    height: 100%
    display: flex
    flex-direction: column
    overflow: hidden
    :global
        .ant-pagination
            margin-bottom: 0 !important
.gridLayout
  flex: 1
  display: grid
  grid-template-columns: 1.5fr 2fr
  grid-template-rows: repeat(2, 1fr)
  gap: px(10)

.chartsContainer
  display: flex
  gap: px(20)
  align-items: center
  justify-content: space-around

.chartItem
  display: flex
  flex-direction: column
  align-items: center
  flex: 1
  span
    color: #fff
    font-size: px(26)
    text-align: center
    font-weight: bold
    font-family: 'alibabaPuhuiM'

.datePicker
  margin-bottom: px(10)
  width: px(200)
  :global
    .ant-picker
      background: rgba(0, 0, 0, 0.3)
      border: 1px solid rgba(255, 255, 255, 0.3)
      border-radius: px(6)
      width: 100%

    .ant-picker-input > input
      color: #fff !important
      background: transparent

    .ant-picker-input > input::placeholder
      color: rgba(255, 255, 255, 0.6) !important

    .ant-picker-suffix
      color: rgba(255, 255, 255, 0.8)

    .ant-picker:hover
      border-color: rgba(255, 255, 255, 0.6)

    .ant-picker-focused
      border-color: #1890ff
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2)

