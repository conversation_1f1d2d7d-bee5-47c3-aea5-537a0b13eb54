import React from 'react';
import styles from '../index.sass';

// 重新定义接口以避免循环依赖
interface FaultInfo {
  level: 1 | 2 | 3; // 故障等级：1-严重，2-中等，3-轻微
  title: string; // 响应级别标题
  occurTime: string; // 发生时间
  description: string; // 故障描述
  location: string; // 故障位置
  currentStatus: '处理中' | '待处理' | '已解决'; // 当前状态
}

interface FaultPopupProps {
  visible: boolean;
  x: number;
  y: number;
  faultInfo: FaultInfo | null;
}

/**
 * 故障悬浮弹窗组件
 * 在鼠标悬浮在故障设施/管道上时显示故障详情
 */
const FaultPopup: React.FC<FaultPopupProps> = ({
  visible,
  x,
  y,
  faultInfo,
}) => {
  if (!visible || !faultInfo) {
    return null;
  }

  return (
    <div
      className={styles.faultPopup}
      style={{
        position: 'fixed',
        left: `${x + 10}px`,
        top: `${y - 10}px`,
        zIndex: 1000,
        pointerEvents: 'none',
      }}
    >
      <div className={styles.faultPopupContent}>
        <div
          className={`${styles.faultTitle} ${
            styles[`level${faultInfo.level}`]
          }`}
        >
          {faultInfo.title}
        </div>
        <div className={styles.faultTime}>
          发生时间: {faultInfo.occurTime}
        </div>
        <div className={styles.faultDescription}>
          {faultInfo.description}
        </div>
        <div className={styles.faultLocation}>
          位置: {faultInfo.location}
        </div>
        <div className={styles.faultStatus}>
          状态: {faultInfo.currentStatus}
        </div>
      </div>
    </div>
  );
};

export default FaultPopup;
