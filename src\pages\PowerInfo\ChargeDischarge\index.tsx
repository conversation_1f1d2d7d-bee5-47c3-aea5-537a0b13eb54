import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { option } from './option';
interface Prop {
  data?: number[];
}
export default function (prop: Prop) {
  const {} = prop;
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    myChart.current.setOption(option());
  }, []);

  return (
    <>
      <div className={styles.chart} ref={container}></div>
    </>
  );
}
