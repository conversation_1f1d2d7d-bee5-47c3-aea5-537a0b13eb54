import { px } from '@/utils/util';
import * as echarts from 'echarts';

let data = [];
for (let i = 0; i < 24; i++) {
  data.push({
    time: i + ':00',
    value: (Math.random() * 50 + 100).toFixed(0),
  });
}
export const option = () => {
  return {
    grid: {
      top: px(30),
      left: px(20),
      right: px(20),
      bottom: px(0),
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        fontSize: px(14),
        color: '#fff',
      },
      data: data.map(function (item) {
        return item.time;
      }),
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255,255,255,0.1)',
        },
      },
      axisLabel: {
        fontSize: px(14),
        color: '#fff',
      },
    },
    series: [
      {
        data: data.map(function (item) {
          return item.value;
        }),
        name: '功率',
        type: 'line',
        symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
        showAllSymbol: true,
        symbolSize: 0,
        smooth: true,
        lineStyle: {
          normal: {
            width: px(3),
            color: '#19A3DF', // 使用标准化颜色
          },
          borderColor: 'rgba(0,0,0,.4)',
        },
        itemStyle: {
          color: '#19A3DF',
          borderColor: '#19A3DF',
          borderWidth: 2,
        },
        tooltip: {
          show: true,
        },
        areaStyle: {
          //区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: `${'#19A3DF'}4D`, // 添加30%透明度
                },
                {
                  offset: 1,
                  color: `${'#19A3DF'}00`, // 添加0%透明度
                },
              ],
              false,
            ),
            shadowColor: `${'#19A3DF'}80`, // 添加50%透明度
            shadowBlur: 20,
          },
        },
      },
    ],
  };
};
