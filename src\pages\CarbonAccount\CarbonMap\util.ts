import { px } from '@/utils/util';

export const label2d = (name: string) => {
  const labelDiv = document.createElement('div');
  labelDiv.className = 'label';
  labelDiv.textContent = name;
  labelDiv.style.pointerEvents = 'auto';
  labelDiv.style.background = 'rgba(0,0,0,0.5)';
  labelDiv.style.padding = px(10) + 'px';
  labelDiv.style.color = 'white';
  labelDiv.style.fontSize = px(16) + 'px';
  labelDiv.style.display = 'flex';
  labelDiv.style.alignItems = 'center';
  labelDiv.style.justifyContent = 'center';
  return labelDiv;
};
export const label3d = () => {
  const labelDiv = document.createElement('div');
  labelDiv.className = 'label';
  labelDiv.textContent = '螺杆机001';
  labelDiv.style.pointerEvents = 'auto';
  labelDiv.style.color = 'white';
  labelDiv.style.fontSize = '0.3px';
  labelDiv.style.display = 'flex';
  labelDiv.style.alignItems = 'center';
  labelDiv.style.justifyContent = 'center';
  return labelDiv;
};
