import { px } from '@/utils/util';
import 'echarts-gl';

const getParametricEquation = (
  startRatio,
  endRatio,
  isSelected,
  isHovered,
  k,
  height,
) => {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;

  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果。
  const newIsSelected = startRatio === 0 && endRatio === 1 ? false : isSelected;

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  const newK = typeof k !== 'undefined' ? k : 1 / 3;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = newIsSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = newIsSelected ? Math.sin(midRadian) * 0.1 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u, v) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * newK) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * newK) * hoverRate
        );
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * newK) * hoverRate;
    },

    y: function (u, v) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * newK) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * newK) * hoverRate
        );
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * newK) * hoverRate;
    },

    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u);
      }
      return Math.sin(v) > 0 ? 1 * height : -1;
    },
  };
};

const getPie3D = (pieData, internalDiameterRatio) => {
  let series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let legendData = [];
  let k =
    typeof internalDiameterRatio !== 'undefined'
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
      : 1 / 3;

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;

    let seriesItem = {
      name:
        typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
    };

    if (typeof pieData[i].itemStyle !== 'undefined') {
      let itemStyle = {};
      if (pieData[i].itemStyle.color !== undefined) {
        itemStyle = { ...itemStyle, color: pieData[i].itemStyle.color };
      }
      if (pieData[i].itemStyle.opacity !== undefined) {
        itemStyle = { ...itemStyle, opacity: pieData[i].itemStyle.opacity };
      }

      (seriesItem as any).itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      // 调整扇形高度
      i === 0 ? 10 : 10,
      // 移除多余的参数
    );

    startValue = endValue;

    legendData.push(series[i].name);
  }
  return series;
};

const colors = [
  '#1978E5',
  '#5AD8A6',
  '#00faed',
  '#5B8FF9',
  '#FF6F56',
  '#C48CFF',
];
// const colors1 = [
//   '#1978E5',
//   '#60B45E',
//   '#0ddbd1',
//   '#9B63CD',
//   '#BECDD0',
//   '#E57373',
// ];

const getOption = (data, unit) => {
  // 为每个数据项添加颜色
  const optionsData = data.map((item, index) => ({
    ...item,
    value: item.value, // 根据type选择使用哪个值
    itemStyle: {
      color: colors[index % colors.length],
    },
  }));
  const series = getPie3D(optionsData, 0.3); // 可做为调整内环大小 0为实心圆饼图，大于0 小于1 为圆环
  series.push({
    name: 'pie2d',
    type: 'pie',
    label: { show: false },
    startAngle: 248, //起始角度，支持范围[0, 360]。
    clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
    radius: ['50%', '60%'],
    center: ['65%', '50%'],
    data: optionsData,
    itemStyle: {
      opacity: 0,
    },
  });

  const option = {
    legend: {
      top: 'center',
      left: px(325),
      orient: 'vertical',
      icon: 'circle',
      formatter: (name: string) => {
        let value = 0;
        data.forEach((item) => {
          if (item.name === name) {
            value = item.value;
          }
        });
        // 根据type调整单位显示
        return `${name}(${value}${unit})`;
      },
      textStyle: {
        color: 'white',
        fontSize: px(14),
      },
      itemStyle: {},
    },
    animation: true,
    tooltip: {
      show: false,
      textStyle: {
        fontSize: px(14),
      },
    },
    label: {
      show: false,
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 2,
      left: '-20%',
      bottom: '1%',
      viewControl: {
        distance: 160,
        alpha: 30,
        beta: 90,
        zoomSensitivity: 0,
        rotateSensitivity: 1,
      },
    },
    series: series,
  };

  return option;
};

export default getOption;
