import { px, routerMap } from '@/utils/util';
import { ConfigProvider, Dropdown } from 'antd';
import styles from './index.sass';
interface IProp {
  onClick: (params: { path: string; name: string }) => void;
}
const MyDropdown = (prop: IProp) => {
  const { onClick } = prop;
  const items2 = routerMap.map((item) => {
    return {
      key: item.path,
      label: (
        <button
          type="button"
          onClick={() => {
            onClick({
              path: item.path,
              name: item.name,
            });
          }}
          className={styles.btn}
        >
          {item.name}
        </button>
      ),
    };
  });

  return (
    <ConfigProvider
      theme={{
        token: {
          /* 这里是你的全局 token */
          fontSize: px(16),
          controlHeight: px(30),
        },
        components: {
          Dropdown: {
            paddingXXS: px(2),
          },
        },
      }}
    >
      <div className={styles.box}>
        <Dropdown
          trigger={['click']}
          menu={{ items: items2 }}
          overlayClassName={styles.menu}
          placement={'bottom'}
        >
          <div className={styles.menus}>
            <img src="/menus.png" alt="" />
          </div>
        </Dropdown>
      </div>
    </ConfigProvider>
  );
};

export default MyDropdown;
