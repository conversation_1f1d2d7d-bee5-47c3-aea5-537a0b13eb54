import CustomSelectV2 from '@/components/CustomSelectV2';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { option } from './option';
interface Prop {
  data?: number[];
}
export default function (prop: Prop) {
  const {} = prop;
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    myChart.current.setOption(option());
  }, []);

  return (
    <>
      <CustomSelectV2
        className={styles.select}
        defaultValue={'电能'}
        options={[
          {
            label: '电能',
            value: '电能',
          },
          {
            label: '风能',
            value: '风能',
          },
          {
            label: '水能',
            value: '水能',
          },
        ]}
      ></CustomSelectV2>
      <div className={styles.chart} ref={container}></div>
    </>
  );
}
