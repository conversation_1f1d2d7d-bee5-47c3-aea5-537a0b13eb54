module.exports = {
  extends: require.resolve('@umijs/max/eslint'),
  ignorePatterns: ['.umirc.ts', 'shim.d.ts'],
  rules: {
    'react/no-unknown-property': [
      'error',
      {
        ignore: [
          'flex',
          'w',
          'mr',
          'ml',
          'm',
          'mb',
          'h',
          'text',
          'p',
          'pt',
          'pr',
          'pl',
          'bg',
          'rounded',
          'mt',
          'items',
          'object',
          'position',
          'intensity',
          'args',
        ],
      },
    ],
  },
};
