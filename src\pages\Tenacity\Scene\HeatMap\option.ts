export const option = () => {
  // 区域列标签 (A-H列，8列)
  const columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

  // 区域行标签 (4-1行，从上到下)
  const rows = ['4', '3', '2', '1'];

  // 颜色映射函数
  const getColor = (value: number) => {
    if (value <= 1) return '#4CAF50'; // 绿色
    if (value <= 2) return '#8BC34A'; // 浅绿
    if (value <= 3) return '#CDDC39'; // 黄绿
    if (value <= 4) return '#FFEB3B'; // 黄色
    if (value <= 5) return '#FFC107'; // 橙黄
    if (value <= 6) return '#FF9800'; // 橙色
    if (value <= 7) return '#FF5722'; // 深橙
    return '#F44336'; // 红色
  };

  // 预设的随机韧性指数分布 (4行8列)
  const predefinedValues = [
    [3, 7, 2, 5, 8, 1, 6, 4], // 第4行 (A4-H4)
    [6, 2, 8, 3, 1, 7, 4, 5], // 第3行 (A3-H3)
    [1, 5, 4, 7, 6, 3, 8, 2], // 第2行 (A2-H2)
    [4, 8, 6, 1, 3, 5, 2, 7], // 第1行 (A1-H1)
  ];

  // 生成热力图数据
  // 格式: [列索引, 行索引, 值]
  const generateHeatmapData = () => {
    const data = [];
    // 为每个单元格生成数据 (4行8列)
    for (let i = 0; i < rows.length; i++) {
      for (let j = 0; j < columns.length; j++) {
        // 从预设数组中获取韧性指数值
        const value = predefinedValues[i][j];

        // 添加单元格标签
        const cellLabel = columns[j] + rows[i];

        // 添加到数据数组 - 确保值在可视化范围内
        data.push({
          value: [j, i, value],
          cellLabel: cellLabel, // 添加区域标签用于tooltip
          itemStyle: {
            color: getColor(value),
            borderColor: '#1a237e',
            borderWidth: 2,
            borderRadius: 4,
          },
          label: {
            show: true,
            formatter: cellLabel,
            color: '#fff',
            fontSize: 13,
            fontWeight: 'bold',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2,
          },
        });
      }
    }
    return data;
  };

  const heatmapData = generateHeatmapData();
  return {
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        const value = params.data.value[2]; // 韧性指数值
        const cellLabel = params.data.cellLabel; // 区域标签
        return `区域: ${cellLabel}<br/>韧性指数: ${value}`;
      },
    },
    grid: {
      height: '85%',
      width: '95%',
      top: '5%',
      left: '2.5%',
      containLabel: false,
    },
    // 移除坐标轴显示
    xAxis: {
      show: false,
      type: 'category',
      data: columns,
      splitArea: {
        show: false,
      },
    },
    yAxis: {
      show: false,
      type: 'category',
      data: rows,
      splitArea: {
        show: false,
      },
    },
    visualMap: {
      show: true, // 必须显示才能生效，但我们把它放到看不见的地方
      type: 'continuous',
      min: 1,
      max: 9,
      left: -1000, // 移到屏幕外，这样用户看不到但功能仍然生效
      top: -1000,
      color: [
        '#4CAF50', // 绿色 - 低韧性
        '#8BC34A', // 浅绿
        '#CDDC39', // 黄绿
        '#FFEB3B', // 黄色
        '#FFC107', // 橙黄 - 中韧性
        '#FF9800', // 橙色
        '#FF5722', // 深橙
        '#F44336', // 红色 - 高韧性
      ],
    },
    series: [
      {
        name: '区域韧性',
        type: 'heatmap',
        data: heatmapData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
};
