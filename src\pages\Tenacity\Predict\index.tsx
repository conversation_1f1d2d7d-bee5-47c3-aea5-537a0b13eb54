import { px } from '@/utils/util';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';

interface PredictionData {
  time: Date;
  solar: number;
  wind: number;
  load: number;
  total: number;
}

export default function PredictPage() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [predictionData, setPredictionData] = useState<PredictionData[]>([]);
  const [activeChartType, setActiveChartType] = useState('power');
  console.log(currentTime);

  const predictionChartRef = useRef<HTMLDivElement>(null);
  const predictionChartInstance = useRef<echarts.ECharts | null>(null);
  const initializePredictionData = () => {
    const now = new Date();
    const data: PredictionData[] = [];

    for (let i = 0; i < 24; i++) {
      const time = new Date(now.getTime() + i * 60 * 60 * 1000);
      const hour = time.getHours();

      // 光伏出力：白天有出力，夜间为0
      const solarFactor =
        hour >= 6 && hour <= 18 ? Math.sin(((hour - 6) * Math.PI) / 12) : 0;
      const solar = solarFactor * (25 + Math.random() * 20);

      // 风电出力：相对稳定，有一定波动
      const wind = 12 + Math.sin((hour * Math.PI) / 12) * 8 + Math.random() * 6;

      // 负荷需求：工作时间较高，夜间较低
      const loadFactor = hour >= 8 && hour <= 20 ? 1.2 : 0.8;
      const load = loadFactor * (70 + Math.random() * 25);

      data.push({
        time: time,
        solar: Math.max(0, solar),
        wind: Math.max(5, wind),
        load: Math.max(50, load),
        total: Math.max(0, solar) + Math.max(5, wind),
      });
    }

    setPredictionData(data);
  };
  const updatePredictionChart = (type: string) => {
    if (!predictionChartInstance.current || !predictionData.length) return;

    let option: any = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(25, 65, 100, 0.9)',
        borderColor: 'rgba(64, 128, 255, 0.5)',
        textStyle: { color: '#e0f0ff' },
        formatter: function (params: any) {
          let result = params[0].axisValueLabel + '<br/>';
          params.forEach((param: any) => {
            result += `${param.marker} ${
              param.seriesName
            }: ${param.value[1].toFixed(1)} MW<br/>`;
          });
          return result;
        },
      },
      legend: {
        textStyle: { color: '#8cb3d9' },
        top: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        axisLine: { lineStyle: { color: 'rgba(64, 128, 255, 0.3)' } },
        axisLabel: {
          color: '#8cb3d9',
          formatter: function (value: any) {
            const date = new Date(value);
            return `${date.getHours()}:00`;
          },
        },
        splitLine: { show: false },
      },
      yAxis: {
        type: 'value',
        name: '功率 (MW)',
        nameTextStyle: { color: '#8cb3d9' },
        axisLine: { lineStyle: { color: 'rgba(64, 128, 255, 0.3)' } },
        axisLabel: { color: '#8cb3d9' },
        splitLine: {
          lineStyle: {
            color: 'rgba(64, 128, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
    };

    if (type === 'power') {
      option.legend.data = ['光伏预测', '风电预测', '总出力预测'];
      option.series = [
        {
          name: '光伏预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.solar]),
          lineStyle: { color: '#ffc107', width: 2 },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(255, 193, 7, 0.3)' },
                { offset: 1, color: 'rgba(255, 193, 7, 0.05)' },
              ],
            },
          },
        },
        {
          name: '风电预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.wind]),
          lineStyle: { color: '#4facfe', width: 2 },
        },
        {
          name: '总出力预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.total]),
          lineStyle: { color: '#4caf50', width: 3 },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(76, 175, 80, 0.2)' },
                { offset: 1, color: 'rgba(76, 175, 80, 0.05)' },
              ],
            },
          },
        },
      ];
    } else if (type === 'load') {
      option.legend.data = ['总负荷预测', '工业负荷预测', '民用负荷预测'];
      option.series = [
        {
          name: '总负荷预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.load]),
          lineStyle: { color: '#ff6b6b', width: 2 },
        },
        {
          name: '工业负荷预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.load * 0.6]),
          lineStyle: { color: '#4facfe', width: 2 },
        },
        {
          name: '民用负荷预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.load * 0.4]),
          lineStyle: { color: '#4caf50', width: 2 },
        },
      ];
    } else if (type === 'weather') {
      option.legend.data = ['温度预测', '湿度预测', '风速预测'];
      option.yAxis.name = '气象参数';
      option.series = [
        {
          name: '温度预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [
            d.time,
            25 + Math.sin((d.time.getHours() * Math.PI) / 12) * 8,
          ]),
          lineStyle: { color: '#ff9800', width: 2 },
        },
        {
          name: '湿度预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, 60 + Math.random() * 20]),
          lineStyle: { color: '#2196f3', width: 2 },
        },
        {
          name: '风速预测',
          type: 'line',
          smooth: true,
          data: predictionData.map((d) => [d.time, d.wind * 0.8]),
          lineStyle: { color: '#607d8b', width: 2 },
        },
      ];
    }

    predictionChartInstance.current.setOption(option, true);
  };
  // 初始化预测数据
  useEffect(() => {
    initializePredictionData();
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 初始化图表
  useEffect(() => {
    if (predictionChartRef.current && predictionData.length > 0) {
      if (!predictionChartInstance.current) {
        predictionChartInstance.current = echarts.init(
          predictionChartRef.current,
        );
      }
      updatePredictionChart(activeChartType);
    }
  }, [predictionData, activeChartType]);

  // 计算预测指标
  const getMetrics = () => {
    if (!predictionData.length)
      return { maxRenewable: 0, maxLoad: 0, accuracy: 94.2, riskPoints: 0 };

    const maxRenewable = Math.max(...predictionData.map((d) => d.total));
    const maxLoad = Math.max(...predictionData.map((d) => d.load));
    const riskPoints = predictionData.filter(
      (d) => d.total < d.load * 0.8,
    ).length;

    return { maxRenewable, maxLoad, accuracy: 94.2, riskPoints };
  };

  const metrics = getMetrics();

  // 格式化时间显示
  const formatTime = (date: Date) => {
    return date
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      })
      .replace(/\//g, '-');
  };
  console.log(formatTime(currentTime));

  return (
    <div className={styles.container}>
      {/* 装饰光效 */}
      <div className={styles.glow + ' ' + styles.glow1}></div>
      <div className={styles.glow + ' ' + styles.glow2}></div>

      <div className={styles.mainContent}>
        {/* 左侧面板 */}
        <div className={styles.leftPanel}>
          {/* 预测输入数据 */}
          <div className={styles.panel}>
            <div className={styles.panelHeader}>
              <i className="fas fa-file-import"></i> 预测输入数据
            </div>
            <div className={styles.panelBody}>
              <div className={styles.dataImportRow}>
                <div className={styles.dataGroup}>
                  <div className={styles.dataGroupTitle}>
                    <i className="fas fa-file-excel"></i> 新能源历史数据导入
                  </div>
                  <div className={styles.uploadArea}>
                    <div className={styles.uploadIcon}>
                      <i className="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div className={styles.uploadText}>
                      <div>点击或拖拽上传新能源出力数据</div>
                      <div className={styles.uploadHint}>
                        支持 .xlsx, .csv 格式，包含光伏、风电、储能数据
                      </div>
                    </div>
                    <input
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      style={{ display: 'none' }}
                    />
                  </div>
                </div>

                <div className={styles.dataGroup}>
                  <div className={styles.dataGroupTitle}>
                    <i className="fas fa-file-excel"></i> 负荷历史数据导入
                  </div>
                  <div className={styles.uploadArea}>
                    <div className={styles.uploadIcon}>
                      <i className="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div className={styles.uploadText}>
                      <div>点击或拖拽上传负荷历史数据</div>
                      <div className={styles.uploadHint}>
                        支持 .xlsx, .csv 格式，包含总负荷、工业负荷、民用负荷
                      </div>
                    </div>
                    <input
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      style={{ display: 'none' }}
                    />
                  </div>
                </div>
              </div>

              <div className={styles.dataGroup}>
                <div className={styles.dataGroupTitle}>
                  <i className="fas fa-chart-line"></i> 历史数据概览
                </div>
                <div className={styles.dataSummary}>
                  <div className={styles.summaryItem}>
                    <div className={styles.summaryLabel}>新能源平均出力</div>
                    <div className={styles.summaryValue}>25.6 MW</div>
                  </div>
                  <div className={styles.summaryItem}>
                    <div className={styles.summaryLabel}>平均负荷需求</div>
                    <div className={styles.summaryValue}>78.3 MW</div>
                  </div>
                  <div className={styles.summaryItem}>
                    <div className={styles.summaryLabel}>极端天气事件</div>
                    <div className={styles.summaryValue}>3次</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 预测参数设置 */}
          <div className={styles.panel}>
            <div className={styles.panelHeader}>
              <i className="fas fa-sliders-h"></i> 预测参数设置
            </div>
            <div className={styles.panelBody}>
              <div className={styles.dataGroup}>
                <div className={styles.dataItemRow}>
                  <div className={styles.dataItemHalf}>
                    <div className={styles.dataLabel}>开始时间</div>
                    <input
                      type="datetime-local"
                      className={styles.inputField}
                    />
                  </div>
                  <div className={styles.dataItemHalf}>
                    <div className={styles.dataLabel}>结束时间</div>
                    <input
                      type="datetime-local"
                      className={styles.inputField}
                    />
                  </div>
                </div>
                <div className={styles.dataItemRow}>
                  <div className={styles.dataItemQuarter}>
                    <div className={styles.dataLabel}>预测步长</div>
                    <select className={styles.inputField}>
                      <option value="15">15分钟</option>
                      <option value="30">30分钟</option>
                      <option value="60">1小时</option>
                      <option value="120">2小时</option>
                    </select>
                  </div>
                  <div className={styles.quickSelectInline}>
                    <button type="button" className={styles.quickBtn}>
                      未来24小时
                    </button>
                    <button type="button" className={styles.quickBtn}>
                      未来3天
                    </button>
                    <button type="button" className={styles.quickBtn}>
                      未来7天
                    </button>
                  </div>
                </div>
              </div>

              <div className={styles.dataGroup}>
                <div className={styles.dataItemRow}>
                  <div className={styles.dataItemThird}>
                    <div className={styles.dataLabel}>台风等级</div>
                    <select className={styles.inputField}>
                      <option>无台风影响</option>
                      <option>12级台风</option>
                      <option>14级台风</option>
                      <option>16级台风</option>
                    </select>
                  </div>
                  <div className={styles.dataItemThird}>
                    <div className={styles.dataLabel}>高温阈值</div>
                    <input
                      type="number"
                      className={styles.inputField}
                      defaultValue="38"
                      placeholder="°C"
                    />
                  </div>
                </div>
              </div>

              <div className={styles.actions}>
                <button
                  type="button"
                  className={styles.btn + ' ' + styles.btnPrimary}
                  onClick={initializePredictionData}
                >
                  <i className="fas fa-play"></i> 开始预测
                </button>
                <button type="button" className={styles.btn}>
                  <i className="fas fa-save"></i> 保存配置
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧结果展示区 */}
        <div className={styles.rightPanel}>
          <div className={styles.panel}>
            <div className={styles.panelHeader}>
              <i className="fas fa-chart-line"></i> 预测分析结果
            </div>
            <div className={styles.panelBody}>
              <div className={styles.resultPanel}>
                {/* 预测指标卡片 */}
                <div className={styles.metrics}>
                  <div className={styles.metricCard}>
                    <div className={styles.metricTitle}>新能源出力预测</div>
                    <div className={styles.metricValue}>
                      {metrics.maxRenewable.toFixed(1)} MW
                    </div>
                    <div className={styles.metricSubtitle}>24小时峰值</div>
                  </div>
                  <div className={styles.metricCard}>
                    <div className={styles.metricTitle}>负荷需求预测</div>
                    <div className={styles.metricValue}>
                      {metrics.maxLoad.toFixed(1)} MW
                    </div>
                    <div className={styles.metricSubtitle}>极端条件下</div>
                  </div>
                  <div className={styles.metricCard}>
                    <div className={styles.metricTitle}>预测准确率</div>
                    <div className={styles.metricValue}>
                      {metrics.accuracy}%
                    </div>
                    <div className={styles.metricSubtitle}>模型置信度</div>
                  </div>
                  <div className={styles.metricCard}>
                    <div className={styles.metricTitle}>风险预警</div>
                    <div
                      className={
                        styles.metricValue +
                        ' ' +
                        (metrics.riskPoints > 0 ? styles.pulse : '')
                      }
                    >
                      {metrics.riskPoints > 0
                        ? `${metrics.riskPoints}处`
                        : '无风险'}
                    </div>
                    <div className={styles.metricSubtitle}>需重点关注</div>
                  </div>
                </div>

                {/* 预测图表 */}
                <div className={styles.chartContainer}>
                  <div className={styles.chartHeader}>
                    <div className={styles.chartTitle}>
                      <i className="fas fa-chart-area"></i> 预测结果曲线
                    </div>
                    <div className={styles.chartControls}>
                      <button
                        type="button"
                        className={
                          styles.chartBtn +
                          (activeChartType === 'power'
                            ? ' ' + styles.active
                            : '')
                        }
                        onClick={() => setActiveChartType('power')}
                      >
                        功率预测
                      </button>
                      <button
                        type="button"
                        className={
                          styles.chartBtn +
                          (activeChartType === 'load'
                            ? ' ' + styles.active
                            : '')
                        }
                        onClick={() => setActiveChartType('load')}
                      >
                        负荷预测
                      </button>
                      <button
                        type="button"
                        className={
                          styles.chartBtn +
                          (activeChartType === 'weather'
                            ? ' ' + styles.active
                            : '')
                        }
                        onClick={() => setActiveChartType('weather')}
                      >
                        气象预测
                      </button>
                    </div>
                  </div>
                  <div className={styles.chartContent}>
                    <div
                      ref={predictionChartRef}
                      style={{ width: '100%', height: `${px(240)}px` }}
                    ></div>
                  </div>
                </div>

                {/* 预测结果详情 */}
                <div className={styles.predictionDetails}>
                  <div className={styles.detailCard}>
                    <div className={styles.detailHeader}>
                      <div className={styles.detailTitle}>光伏出力预测</div>
                      <div
                        className={styles.detailStatus + ' ' + styles.success}
                      >
                        预测正常
                      </div>
                    </div>
                    <div className={styles.detailContent}>
                      <div className={styles.detailRow}>
                        <div className={styles.detailItem}>
                          <span>当前出力:</span>
                          <span>
                            {predictionData.length > 0
                              ? predictionData[0].solar.toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                        <div className={styles.detailItem}>
                          <span>24h峰值:</span>
                          <span>
                            {predictionData.length > 0
                              ? Math.max(
                                  ...predictionData.map((d) => d.solar),
                                ).toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                      </div>
                      <div className={styles.detailRow}>
                        <div className={styles.detailItem}>
                          <span>24h谷值:</span>
                          <span>
                            {predictionData.length > 0
                              ? Math.min(
                                  ...predictionData.map((d) => d.solar),
                                ).toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                        <div className={styles.detailItem}>
                          <span>平均出力:</span>
                          <span>
                            {predictionData.length > 0
                              ? (
                                  predictionData.reduce(
                                    (sum, d) => sum + d.solar,
                                    0,
                                  ) / predictionData.length
                                ).toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className={styles.detailCard}>
                    <div className={styles.detailHeader}>
                      <div className={styles.detailTitle}>风电出力预测</div>
                      <div
                        className={styles.detailStatus + ' ' + styles.warning}
                      >
                        台风影响
                      </div>
                    </div>
                    <div className={styles.detailContent}>
                      <div className={styles.detailRow}>
                        <div className={styles.detailItem}>
                          <span>当前出力:</span>
                          <span>
                            {predictionData.length > 0
                              ? predictionData[0].wind.toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                        <div className={styles.detailItem}>
                          <span>24h峰值:</span>
                          <span>
                            {predictionData.length > 0
                              ? Math.max(
                                  ...predictionData.map((d) => d.wind),
                                ).toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                      </div>
                      <div className={styles.detailRow}>
                        <div className={styles.detailItem}>
                          <span>24h谷值:</span>
                          <span>
                            {predictionData.length > 0
                              ? Math.min(
                                  ...predictionData.map((d) => d.wind),
                                ).toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                        <div className={styles.detailItem}>
                          <span>平均出力:</span>
                          <span>
                            {predictionData.length > 0
                              ? (
                                  predictionData.reduce(
                                    (sum, d) => sum + d.wind,
                                    0,
                                  ) / predictionData.length
                                ).toFixed(1)
                              : '0'}{' '}
                            MW
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 预测建议 */}
                <div className={styles.recommendations}>
                  <div className={styles.recommendationsHeader}>
                    <div className={styles.recommendationsTitle}>
                      <i className="fas fa-lightbulb"></i> 预测分析建议
                    </div>
                  </div>
                  <div className={styles.recommendationsContent}>
                    <ul className={styles.recommendationsList}>
                      <li>
                        <strong>储能系统预充电</strong> -
                        在台风到达前4小时将储能系统充电至90%以上
                      </li>
                      <li>
                        <strong>负荷调度预案</strong> -
                        预计台风期间负荷需求增加15%，建议启动备用电源
                      </li>
                      <li>
                        <strong>新能源出力补偿</strong> -
                        风电出力将大幅下降，需提前协调火电机组备用
                      </li>
                      <li>
                        <strong>关键设备保护</strong> -
                        预测显示东南区域风速最大，建议提前断开敏感设备
                      </li>
                    </ul>
                    <button
                      type="button"
                      className={styles.scenarioBtn}
                      onClick={() => console.log('生成场景')}
                    >
                      <i className="fas fa-magic"></i> 场景生成
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
