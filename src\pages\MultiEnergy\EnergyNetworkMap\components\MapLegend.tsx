import React from 'react';
import styles from '../index.sass';

/**
 * 地图图例组件
 * 显示管道类型和设施状态的图例说明
 */
const MapLegend: React.FC = () => {
  return (
    <div className={styles.legend}>
      <div className={styles.legendTitle}>图例</div>
      <div className={styles.legendItem}>
        <div className={`${styles.legendIcon} ${styles.heat}`}></div>
        <span>热网管道</span>
      </div>
      <div className={styles.legendItem}>
        <div className={`${styles.legendIcon} ${styles.gas}`}></div>
        <span>气网管道</span>
      </div>
      <div className={styles.legendItem}>
        <div className={`${styles.legendDot} ${styles.running}`}></div>
        <span>运行中</span>
      </div>
      <div className={styles.legendItem}>
        <div className={`${styles.legendDot} ${styles.maintenance}`}></div>
        <span>维护中</span>
      </div>
      <div className={styles.legendItem}>
        <div className={`${styles.legendDot} ${styles.error}`}></div>
        <span>故障</span>
      </div>
    </div>
  );
};

export default MapLegend;
