import CustomSubTitle from '@/components/CustomSubTitle';
import CustomTitle from '@/components/CustomTitle';
import { EditOutlined } from '@ant-design/icons';
import styles from './index.sass';
export default function () {
  return (
    <div className={styles.box}>
      <CustomTitle>参数设置</CustomTitle>
      <CustomSubTitle className={styles.subtitle}>
        气象要素&nbsp;&nbsp;
        <EditOutlined />
      </CustomSubTitle>
      <div className={styles.content}>
        <div className={styles.item}>
          <div className={styles.label}>气温：</div>
          <div className={styles.value}>30℃</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>湿度：</div>
          <div className={styles.value}>2%</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>天气：</div>
          <div className={styles.value}>多云</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>云量：</div>
          <div className={styles.value}>20%</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>气压：</div>
          <div className={styles.value}>100千帕</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>能见度：</div>
          <div className={styles.value}>20公里</div>
        </div>
      </div>
      <div className={styles.filter}></div>
      <CustomSubTitle className={styles.subtitle}>
        反演计算入参&nbsp;&nbsp;
        <EditOutlined />
      </CustomSubTitle>
      <div className={styles.content}>
        <div className={styles.item}>
          <div className={styles.label}>系数1：</div>
          <div className={styles.value}>0.63</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>系数2：</div>
          <div className={styles.value}>0.63</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>系数3：</div>
          <div className={styles.value}>0.63</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>系数4：</div>
          <div className={styles.value}>0.63</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>系数5：</div>
          <div className={styles.value}>0.63</div>
        </div>
        <div className={styles.item}>
          <div className={styles.label}>系数6：</div>
          <div className={styles.value}>0.63</div>
        </div>
      </div>
      <div className={styles.filter}></div>
    </div>
  );
}
