import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';
import styles from './index.sass';
import { getOption } from './option';

interface Props {
  name?: string;
}

const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const completeBox = useRef<HTMLDivElement>(null);
  const proportionBox = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Static data for display purposes
    const completeRate = 0.85; // 85% completion rate
    const usagePercent = 0.72; // 72% usage percentage

    let completeChart = echarts.init(completeBox.current as HTMLDivElement);
    completeChart.setOption(getOption(completeRate * 100, 100, 'complete'));

    let proportionChart = echarts.init(proportionBox.current as HTMLDivElement);
    proportionChart.setOption(getOption(usagePercent * 100, 100, 'proportion'));
  }, []);
  return (
    <div className={styles.box}>
      <div className={styles.completeTitle}>最新调度计划完成率</div>
      <div ref={completeBox} className={styles.completeBox}></div>
      <div className={styles.proportionTitle}>最新调度计划资源使用占比</div>
      <div ref={proportionBox} className={styles.proportionBox}></div>
    </div>
  );
};

export default Index;
