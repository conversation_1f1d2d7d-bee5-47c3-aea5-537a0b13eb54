import { px } from '@/utils/util';
import { ConfigProvider, Select, SelectProps } from 'antd'; // 注意这里导入了TableProps类型

export default function (props: SelectProps) {
  return (
    <ConfigProvider
      theme={{
        token: { fontSize: px(18), controlHeight: px(35) },
        components: {
          Select: {
            colorBgContainer: 'rgba(0,0,0,0)',
            colorBorder: '#fff',
            colorTextPlaceholder: '#fff',
            colorText: '#fff',
            colorTextQuaternary: '#fff',
            colorBgElevated: '#0D2C5C',
            optionSelectedBg: '#184E90',
          },
        },
      }}
    >
      <Select {...props} />
    </ConfigProvider>
  );
}
