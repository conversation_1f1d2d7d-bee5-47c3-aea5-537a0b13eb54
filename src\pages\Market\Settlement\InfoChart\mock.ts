// mock.ts - 多能源收益占比数据
const mockData = [
  { name: '光伏发电', value: 18700 }, // 光伏发电总收益：4930+4420+5440+3910=18700万元
  { name: '风力发电', value: 29512 }, // 风力发电总收益：11284+8432+9796=29512万元
  { name: '储能电站', value: 5280 }, // 储能电站总收益：2880+2400=5280万元
  { name: '工业余热', value: 9126 }, // 工业余热总收益：3510+2652+2964=9126万元
  { name: '生物质发电', value: 3040 }, // 生物质发电总收益：3040万元
  { name: '垃圾发电', value: 3840 }, // 垃圾发电总收益：3840万元
  { name: '潮汐发电', value: 1080 }, // 潮汐发电总收益：1080万元
];
const mockUnit = '万元';
const mockType = '收益'; // 展示收益金额

export { mockData, mockType, mockUnit };
