@import '@/utils/helpers.sass'

.container
  display: flex
  flex-direction: column
  align-items: center
  width: px(160)
  height: px(120)

.valueContainer
  width: 100%
  height: 100%
  background-image: url('/bottom.png')
  background-size: 100% 100%
  background-repeat: no-repeat
  display: flex
  flex-direction: column
  align-items: center
  justify-content: center
  position: relative

  &::before
    content: ''
    position: absolute
    top: 20%
    left: 50%
    transform: translate(-50%, -50%)
    width: 70%
    height: px(88)
    background: linear-gradient(to right, rgba(0, 145, 255, 0.3) 0%, rgba(0, 145, 255, 0) 50%, rgba(0, 145, 255, 0.3) 100%)
    z-index: 0

.value
  margin-bottom: px(60)
  font-size: px(48)
  color: linear-gradient(to bottom, #0095ff, #00e4ff)
  font-weight: bold
  text-shadow: 0 0 px(10) rgba(0, 145, 255, 0.5)
  background: linear-gradient(to bottom, #0095ff, #96ecf5)
  -webkit-background-clip: text
  -webkit-text-fill-color: transparent
  position: relative
  z-index: 1

// .dots
//   position: absolute
//   right: px(0)
//   color: #00E4FF
//   font-size: px(24)

.label
  margin-top: px(10)
  color: #ffffff
  font-size: px(16) 