@import '@/utils/helpers.sass'
.tabs
    display: flex
    width: 100%
    height: px(50)
    align-items: center
    font-size: px(24)
    color: white
.item
    width: 25%
    background: #0F4484
    height: 100%
    display: flex
    justify-content: center
    align-items: center
    cursor: pointer
    color: #9ac8fd
    font-family: pangmen

    .arrow
        margin: 0 px(10)
        width: px(35)
        height: px(28)
    &:hover
        color: white
        background: linear-gradient(to right, rgba(15, 68, 132, 1), #1166ce,#1166ce,rgba(15, 68, 132, 1))
        .arrow
            content: url(/icon3.png)
.itemIcon
    margin: 0 px(10)
    width: px(35)
    height: px(35)
.active
    color: white
    background: linear-gradient(to right, rgba(15, 68, 132, 1), #1166ce,#1166ce,rgba(15, 68, 132, 1))
