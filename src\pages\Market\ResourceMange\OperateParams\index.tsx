import { px } from '@/utils/util';
import { useAntdTable } from 'ahooks';
import { ConfigProvider, Form, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import styles from './index.sass';

// mock数据函数
function getMockOperateParamsTableData(params: {
  pageSize: number;
  current: number;
}) {
  const allList = [
    { name: 'EQ-001', power: '120kW', soc: '85%', health: '98%' },
    { name: 'EQ-002', power: '90kW', soc: '85%', health: '95%' },
    { name: 'EQ-003', power: '110kW', soc: '85%', health: '92%' },
    { name: 'EQ-004', power: '60kW', soc: '85%', health: '90%' },
    { name: 'EQ-005', power: '130kW', soc: '85%', health: '99%' },
    { name: 'EQ-006', power: '100kW', soc: '85%', health: '97%' },
    { name: 'EQ-007', power: '80kW', soc: '85%', health: '93%' },
  ];
  const { pageSize, current } = params;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  return Promise.resolve({
    total: allList.length,
    list: allList.slice(start, end),
  });
}

export default function () {
  const [form] = Form.useForm();
  const getTableData = (params: { pageSize: number; current: number }) => {
    return getMockOperateParamsTableData(params);
  };

  const { tableProps } = useAntdTable(getTableData, {
    defaultParams: [
      {
        current: 1,
        pageSize: 4,
      },
    ],
    form,
    defaultType: 'advance',
  });
  const columns: ColumnsType<any> = [
    {
      title: '设备编号',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '实时功率',
      dataIndex: 'power',
      key: 'power',
      align: 'center',
    },
    {
      title: '荷电状态',
      dataIndex: 'soc',
      key: 'soc',
      align: 'center',
    },
    {
      title: '健康指数',
      dataIndex: 'health',
      key: 'health',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      align: 'center',
      render: () => <a>详情</a>,
    },
  ];
  return (
    <ConfigProvider
      theme={{
        token: {},
        components: {
          Table: {
            padding: px(5),
            colorBgContainer: '#0E4280',
            headerBg: '#0E4280',
            borderColor: '#1C72E2',
            colorText: '#fff',
            colorTextHeading: '#fff',
            lineType: 'dashed',
            cellPaddingBlock: px(12),
          },
          Tag: {
            colorTextLightSolid: '#000',
          },
          Form: {
            labelColor: '#fff',
          },
          Pagination: {
            colorText: '#fff',
            itemBg: 'rgba(0,0,0,0)',
            colorBgContainer: 'rgba(0,0,0,0)',
            colorPrimary: '#fff',
            colorTextDisabled: '#999',
          },
        },
      }}
    >
      <div className={styles.box}>
        <Table
          className={styles.table}
          columns={columns}
          {...tableProps}
        ></Table>
      </div>
    </ConfigProvider>
  );
}
