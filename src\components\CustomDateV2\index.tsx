import { px } from '@/utils/util';
import { ConfigProvider, DatePicker, DatePickerProps } from 'antd';

export default function (props: DatePickerProps) {
  return (
    <ConfigProvider
      theme={{
        token: { fontSize: px(18), controlHeight: px(35) },
        components: {
          DatePicker: {
            colorBgContainer: 'rgba(0,0,0,0)',
            colorBgElevated: '#0D2C5C',
            colorText: '#fff',
            colorTextHeading: '#fff',
            colorBorder: '#fff',
            colorIcon: '#fff',
            colorPrimary: 'rgba(24, 93, 158, 1)',
            cellActiveWithRangeBg: 'rgba(24, 93, 158, 1)',
            colorTextPlaceholder: '#999',
          },
        },
      }}
    >
      <DatePicker {...props} />
    </ConfigProvider>
  );
}
