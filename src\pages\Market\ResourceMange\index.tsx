import CustomTitle from '@/components/CustomTitle';
import { px } from '@/utils/util';
import { ConfigProvider, DatePicker } from 'antd';
import dayjs from 'dayjs';
import CapacityChart from './CapacityChart';
import EquipmentTable from './EquipmentTable';
import HealthIndex from './HealthIndex';
import styles from './index.sass';
import Monitor from './Monitor';
import OperateParams from './OperateParams';
export default function () {
  return (
    <ConfigProvider
      theme={{
        token: {},
        components: {
          Table: {
            padding: px(5),
            colorBgContainer: '#0E4280',
            headerBg: '#0E4280',
            borderColor: '#1C72E2',
            colorText: '#fff',
            colorTextHeading: '#fff',
            lineType: 'dashed',
            cellPaddingBlock: px(12),
          },
          Tag: {
            colorTextLightSolid: '#000',
          },
          Form: {
            labelColor: '#fff',
          },
          Pagination: {
            colorText: '#fff',
            itemBg: 'rgba(0,0,0,0)',
            colorBgContainer: 'rgba(0,0,0,0)',
            colorPrimary: '#fff',
            colorTextDisabled: '#999',
          },
          DatePicker: {
            colorBgContainer: 'rgba(0, 0, 0, 0.3)',
            colorBorder: 'rgba(255, 255, 255, 0.3)',
            colorText: '#fff',
            colorTextPlaceholder: 'rgba(255, 255, 255, 0.6)',
            colorIcon: 'rgba(255, 255, 255, 0.8)',
            colorBgElevated: '#1f1f1f',
            colorTextLightSolid: '#fff',
          },
        },
      }}
    >
      <div className={styles.box}>
        <div className={styles.gridLayout}>
          <div className={styles.item1}>
            <CustomTitle>储能实时数据统计</CustomTitle>
            <DatePicker
              className={styles.datePicker}
              defaultValue={dayjs()}
              placeholder="选择时间"
            />
            <div className={styles.chartsContainer}>
              <div className={styles.chartItem}>
                <CapacityChart />
                <span>容量统计</span>
              </div>
              <div className={styles.chartItem}>
                <Monitor />
                <span>实时功率</span>
              </div>
            </div>
          </div>
          <div className={styles.item2}>
            <CustomTitle>智能设备表格</CustomTitle>
            <EquipmentTable />
          </div>
          <div className={styles.item3}>
            <CustomTitle>设备健康指数柱状图</CustomTitle>
            <DatePicker
              className={styles.datePicker}
              defaultValue={dayjs()}
              placeholder="选择时间"
            />
            <HealthIndex data={[]} />
          </div>
          <div className={styles.item4}>
            <CustomTitle>实时运行参数流</CustomTitle>
            <OperateParams />
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
}
