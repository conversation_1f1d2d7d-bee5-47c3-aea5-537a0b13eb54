import React from 'react';
import styles from '../index.sass';

interface LoadingOverlayProps {
  isLoading: boolean;
}

/**
 * 加载覆盖层组件
 * 在地图加载时显示加载提示
 */
const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ isLoading }) => {
  if (!isLoading) {
    return null;
  }

  return (
    <div className={styles.loading}>
      <div className={styles.loadingText}>地图加载中...</div>
    </div>
  );
};

export default LoadingOverlay;
