import React from 'react';
import styles from './index.sass';

const CustomCard: React.FC<{
  title: string;
  value: number;
  unit?: string;
  subText: string;
}> = ({ title, value, unit, subText }) => {
  return (
    <div className={styles.metricCard}>
      <div className={styles.metricTitle}>{title}</div>
      <div className={styles.metricValue}>
        {value}
        {unit}
      </div>
      <div className={styles.metricSubtitle}>{subText}</div>
    </div>
  );
};

export default CustomCard;
