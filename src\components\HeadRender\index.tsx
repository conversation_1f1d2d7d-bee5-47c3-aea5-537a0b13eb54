import { routerMap } from '@/utils/util';
import { history } from '@umijs/max';
import { useState } from 'react';
import MyDropdown from './DropDown';
import styles from './index.sass';
import TimeShow from './TimeShow';
export default function (prop: any) {
  const { matchMenuKeys } = prop;
  const [currentMenu, setCurrentMenu] = useState(
    routerMap.find((i) => i.path === matchMenuKeys?.[1]) || routerMap[0],
  );
  return (
    <div className={styles.head}>
      <div className={styles.title}>综合能源园区管控平台</div>
      <div className={styles.menus}>
        <div
          className={
            styles.btn +
            ' ' +
            (matchMenuKeys[0] === '/market' ? styles.btnact : '')
          }
          onClick={() => {
            history.push('/market');
          }}
        >
          市场模块
        </div>
        <div
          className={
            styles.btn +
            ' ' +
            (matchMenuKeys[0] === '/home' ? styles.btnact : '')
          }
        >
          <MyDropdown
            onClick={(v) => {
              setCurrentMenu(v);
              history.push(v.path);
            }}
          ></MyDropdown>
          <div
            className={styles.dropname}
            onClick={() => {
              history.push(currentMenu.path);
            }}
          >
            {currentMenu.name}
          </div>
        </div>
        <div
          className={
            styles.btn +
            ' ' +
            (matchMenuKeys[0] === '/tenacity' ? styles.btnact : '')
          }
          onClick={() => {
            history.push('/tenacity');
          }}
        >
          韧性模块
        </div>
      </div>
      <div className={styles.rightmenu}>
        <TimeShow></TimeShow>
        <div className={styles.item}>
          <img src="/user.png" alt="" />
          用户1
        </div>
        <div className={styles.item}>
          <img src="/exit.png" alt="" />
          退出登录
        </div>
      </div>
    </div>
  );
  return (
    <div className={styles.head1}>
      <div className={styles.leftBox}>
        <div
          className={
            styles.menuLeft +
            ' ' +
            (matchMenuKeys[0] === '/market' ? styles.menuLeftActive : '')
          }
          onClick={() => {
            history.push('/market');
          }}
        >
          市场
        </div>
      </div>
      <div className={styles.title}>
        <span
          onClick={() => {
            history.push(currentPath);
          }}
        >
          数字孪生—
        </span>
        <MyDropdown
          onClick={({ path }) => {
            setCurrentPath(path);
            history.push(path);
          }}
        ></MyDropdown>
      </div>
      <div className={styles.rightBox}>
        <div
          className={
            styles.menuRight +
            ' ' +
            (matchMenuKeys[0] === '/tenacity' ? styles.menuRightActive : '')
          }
          onClick={() => {
            history.push('/tenacity');
          }}
        >
          韧性
        </div>
      </div>
    </div>
  );
}
