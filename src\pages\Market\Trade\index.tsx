import CustomTable from '@/components/CustomTable';
import { AGetUnusual } from '@/services/trade';
import { useAntdTable } from 'ahooks';
import { Button, ConfigProvider, DatePicker, Form } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import styles from './index.sass';
const defaultTimeRange = [dayjs(), dayjs()];
export default function () {
  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData: any,
  ) => {
    const { pageSize, current } = params;
    const {
      timeRange = defaultTimeRange,
      type = 'unit',
      rank,
      name,
      company,
    } = formData;
    return AGetUnusual({
      name,
      company,
      rank,
      pageSize,
      current,
      type,
      startTime: timeRange[0].format('YYYY-MM-DD'),
      endTime: timeRange[1].format('YYYY-MM-DD'),
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    defaultParams: [
      {
        current: 1,
        pageSize: 13,
      },
    ],
    form,
    defaultType: 'advance',
  });
  const { submit } = search;
  const columns: ColumnsType<any> = [
    {
      title: '交易日期',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
    {
      title: '碳配额成交量',
      dataIndex: 'power',
      key: 'power',
      align: 'center',
    },
    {
      title: '成交单价',
      dataIndex: 'price',
      key: 'price',
      align: 'center',
    },
    {
      title: '收益',
      dataIndex: 'benefit',
      key: 'benefit',
      align: 'center',
    },
    {
      title: '成本',
      dataIndex: 'cost',
      key: 'cost',
      align: 'center',
    },
    {
      title: '利润',
      dataIndex: 'value',
      key: 'value',
      align: 'center',
    },
  ];
  return (
    <ConfigProvider
      theme={{
        token: {},
        components: {
          Tag: {
            colorTextLightSolid: '#000',
          },
          Form: {
            labelColor: '#fff',
          },
        },
      }}
    >
      <div className={styles.box}>
        <div className={styles.top}>
          <div className={styles.item}>
            <img src="/icon1.png" alt="" /> 累计交易量：12120吨
          </div>
          <div className={styles.item}>
            <img src="/icon12.png" alt="" /> 平均交易单价：12元/吨
          </div>
          <div className={styles.item}>
            <img src="/icon9.png" alt="" /> 累计收益：12120元
          </div>
          <div className={styles.item}>
            <img src="/icon10.png" alt="" /> 累计成本：12120元
          </div>

          <div className={styles.item}>
            <img src="/icon11.png" alt="" /> 累计利润：12120元
          </div>
        </div>
        <div className={styles.filter}>
          <Form
            layout="inline"
            form={form}
            initialValues={{
              timeRange: defaultTimeRange,
            }}
          >
            <Form.Item name="timeRange" label="交易时间">
              <DatePicker.RangePicker
                allowClear={false}
                className={styles.date}
                picker="date"
              />
            </Form.Item>
            <Button className={styles.button} type="primary" onClick={submit}>
              查询
            </Button>
          </Form>
        </div>
        <CustomTable columns={columns} {...tableProps}></CustomTable>
      </div>
    </ConfigProvider>
  );
}
