@import '@/utils/helpers.sass'
:root
  --darkBg: #0a192900
  --panelBg: #1A5298
  --cardBg: #0D2C5C
  --accent: #2196f3
  --accentDark: #1976d2
  --warning: #ff9800
  --success: #4caf50
  --danger: #f44336
  --textPrimary: #ffffff
  --textSecondary: #d7d6d6
  --border: #2a4e7a

*
  margin: 0
  padding: 0
  box-sizing: border-box
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif

body
  background-color: var(--darkBg)
  color: var(--textPrimary)
  height: 100vh
  overflow: hidden

.container
  display: grid
  grid-template-rows: px(60) 1fr
  height: 100vh
  padding: px(15)
  gap: px(15)

// 顶部标题栏
.header
  background: linear-gradient(90deg, var(--panelBg), var(--darkBg))
  border-radius: px(12)
  padding: 0 px(20)
  display: flex
  align-items: center
  justify-content: space-between
  box-shadow: 0 px(4) px(20) rgba(0, 0, 0, 0.4)
  border: px(1) solid var(--border)

.headerTitle
  display: flex
  align-items: center
  gap: px(15)
  
  h1
    font-size: 1.5rem
    font-weight: 600
  
  i
    color: var(--accent)
    font-size: 1.8rem

.weatherWarning
  background: rgba(244, 67, 54, 0.2)
  border: px(1) solid var(--danger)
  border-radius: px(20)
  padding: px(5) px(15)
  display: flex
  align-items: center
  gap: px(8)
  
  i
    color: var(--danger)
    animation: pulse 2s infinite

@keyframes pulse
  0%
    opacity: 0.5
  50%
    opacity: 1
  100%
    opacity: 0.5

// 主内容区
.mainContent
  display: grid
  grid-template-columns: 2fr 1fr
  gap: px(15)
  max-height: px(1200)

  > .panel:last-child
    height: 100%
    overflow: hidden
    display: flex
    flex-direction: column

.panel
  background-color: var(--panelBg)
  border-radius: px(12)
  padding: px(20)
  box-shadow: 0 px(4) px(20) rgba(0, 0, 0, 0.3)
  border: px(1) solid var(--border)
  display: flex
  flex-direction: column

.panelHeader
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(15)
  padding-bottom: px(10)
  border-bottom: px(1) solid var(--border)
   

.panelTitle
  font-size: 1.2rem
  font-weight: 600
  display: flex
  align-items: center
  gap: px(10)
  
  i
    color: var(--accent)

// 阶段控制区
.phaseControl
  display: flex
  gap: px(10)
  margin-bottom: px(20)

.phaseBtn
  flex: 1
  padding: px(12)
  border-radius: px(8)
  background-color: var(--cardBg)
  border: px(1) solid var(--border)
  color: var(--textSecondary)
  cursor: pointer
  transition: all 0.3s ease
  text-align: center
  font-weight: 500
  
  &.active
    background: linear-gradient(135deg, var(--accentDark), var(--accent))
    color: white
    border-color: var(--accent)
    box-shadow: 0 0 px(15) rgba(33, 150, 243, 0.4)
  
  &:hover:not(.active)
    border-color: var(--accent)
    color: var(--textPrimary)

.globalControls
  display: flex
  gap: px(15)
  margin-bottom: px(20)

.controlBtn
  flex: 1
  padding: px(12)
  border-radius: px(8)
  background: var(--cardBg)
  border: px(1) solid var(--border)
  color: var(--textPrimary)
  display: flex
  align-items: center
  justify-content: center
  gap: px(8)
  cursor: pointer
  transition: all 0.3s ease
  
  &:hover
    background: var(--accentDark)
    border-color: var(--accent)
  
  &.primary
    background: linear-gradient(135deg, var(--accentDark), var(--accent))
    border-color: var(--accent)
    box-shadow: 0 0 px(10) rgba(33, 150, 243, 0.3)
  
  &.warning
    background: rgba(255, 152, 0, 0.2)
    border-color: var(--warning)
    
    &:hover
      background: var(--warning)

// 资源监控
.resourceTabs
  display: flex
  border-bottom: px(1) solid var(--border)
  margin-bottom: px(15)

.tab
  padding: px(10) px(20)
  cursor: pointer
  border-bottom: px(3) solid transparent
  transition: all 0.3s ease
  
  &.active
    border-bottom: px(3) solid var(--accent)
    color: var(--accent)

.resourceGrid
  display: grid
  grid-template-columns: repeat(4, 1fr)
  gap: px(15)
  flex: 1

.resourceCard
  background-color: var(--cardBg)
  border-radius: px(10)
  padding: px(15)
  border: px(1) solid var(--border)
  min-width: px(250)
  min-height: px(400)

.resourceHeader
  display: flex
  align-items: center
  gap: px(10)
  margin-bottom: px(15)

.resourceIcon
  width: px(40)
  height: px(40)
  border-radius: 50%
  background: rgba(33, 150, 243, 0.2)
  display: flex
  align-items: center
  justify-content: center
  font-size: 1.2rem
  color: var(--accent)

.resourceTitle
  h3
    font-size: 1.1rem
  
  p
    font-size: 0.9rem
    color: var(--textSecondary)

.resourceStats
  display: flex
  justify-content: space-between
  margin-top: px(10)

.stat
  text-align: center

.statValue
  font-size: 1.2rem
  font-weight: 600

.statLabel
  font-size: 0.85rem
  color: var(--textSecondary)

.deviation
  padding: px(3) px(8)
  border-radius: px(20)
  font-size: 0.85rem
  font-weight: 500
  
  &.positive
    background: rgba(76, 175, 80, 0.2)
    color: var(--success)
  
  &.negative
    background: rgba(244, 67, 54, 0.2)
    color: var(--danger)
  
  &.neutral
    background: rgba(255, 152, 0, 0.2)
    color: var(--warning)

// 图表容器
.chartContainer
  height: px(450)
  margin-top: px(15)

// 韧性指标
.metricsGrid
  display: grid
  grid-template-columns: repeat(3, 1fr)
  gap: px(15)
  margin-bottom: px(20)

.metricCard
  background-color: var(--cardBg)
  border-radius: px(10)
  padding: px(15)
  text-align: center
  border: px(1) solid var(--border)

.metricTitle
  font-size: 0.95rem
  margin-bottom: px(10)
  color: var(--textSecondary)

.metricValue
  font-size: 1.8rem
  font-weight: 700
  margin-bottom: px(5)

.metricChange
  font-size: 0.9rem
  padding: px(3) px(10)
  border-radius: px(20)
  display: inline-block

.changePositive
  background: rgba(76, 175, 80, 0.2)
  color: var(--success)

.changeNegative
  background: rgba(76, 175, 80, 0.2)
  color: var(--success)

.metricSubtitle
  margin-top: px(5)
  font-size: 0.8rem
  color: var(--textSecondary)

// 日志和分析容器
.logAnalysisContainer
  display: flex
  flex-direction: column
  gap: px(15)
  height: 150%
  flex: 1
  min-height: px(480)

.logContainer
  flex: 1
  min-height: px(420)
  background-color: var(--cardBg)
  border-radius: px(8)
  padding: px(10)
  overflow-y: auto
  border: px(1) solid var(--border)
  display: flex
  flex-direction: column

  &::-webkit-scrollbar
    width: px(8)
    background: var(--cardBg)
    border-radius: px(8)

  &::-webkit-scrollbar-thumb
    background: linear-gradient(135deg, var(--accentDark), var(--accent))
    border-radius: px(8)

    &:hover
      background: var(--accent)

  &::-webkit-scrollbar-corner
    background: var(--cardBg)

.logEntry
  padding: px(10)
  border-bottom: px(1) solid var(--border)
  display: flex
  gap: px(10)
  
  &:last-child
    border-bottom: none

.logTime
  color: var(--textSecondary)
  font-size: 0.85rem
  min-width: px(70)

.logContent
  flex: 1

.logResource
  display: inline-block
  padding: px(2) px(8)
  border-radius: px(4)
  background: rgba(33, 150, 243, 0.2)
  color: var(--accent)
  font-size: 0.85rem
  margin-right: px(5)

.logWarning
  color: var(--warning)

.logSuccess
  color: var(--success)

.analysisContainer
  flex: 0 1 55%
  min-height: px(200)
  height: 0
  background-color: var(--cardBg)
  border-radius: px(8)
  padding: px(15)
  border: px(1) solid var(--border)
  display: flex
  flex-direction: column

.analysisTitle
  text-align: center
  margin-bottom: px(15)
  color: var(--accent)
  font-weight: 600

.analysisChart
  flex: 1
  height: 100%
  display: flex
  align-items: center
  justify-content: center

  canvas
    width: 100% !important
    height: 100% !important
    max-height: px(280)

.contributionList
  margin-top: px(15)

.contributionItem
  display: flex
  align-items: center
  margin-bottom: px(10)

.contributionBar
  height: px(8)
  border-radius: px(4)
  margin-right: px(10)

.barElectric
  background: linear-gradient(90deg, var(--accent), #64b5f6)
  width: 45%

.barHeat
  background: linear-gradient(90deg, var(--warning), #ffb74d)
  width: 30%

.barGas
  background: linear-gradient(90deg, var(--success), #81c784)
  width: 25%

.contributionLabel
  font-size: 0.9rem
  min-width: px(100)

.contributionValue
  font-weight: 600

// 新增整体容器
.dispatchContainer
  height: 100vh
  overflow: hidden
  padding: px(16)
  background-color: var(--darkBg)
  color: var(--textPrimary)
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif

.mainContent
  display: grid
  grid-template-columns: 2fr 1fr
  gap: px(16)
  height: calc(100vh - px(32))

.leftPanel
  grid-column: 1 / span 1

.rightPanel
  grid-column: 2 / span 1

// 警告按钮
.warnBtn
  background: rgba(255, 152, 0, 0.2) !important
  border: px(1) solid var(--warning) !important
  color: var(--warning) !important
  &:hover
    background: var(--warning) !important
    color: #fff !important

// 阶段控制
.phaseControl
  display: flex
  gap: px(12)
  margin-bottom: px(20)

.phaseBtn
  flex: 1
  padding: px(12)
  border-radius: px(8)
  background-color: var(--cardBg)
  border: px(1) solid var(--border)
  color: var(--textSecondary)
  cursor: pointer
  transition: all 0.3s ease
  text-align: center
  font-weight: 500
  &.active
    background: linear-gradient(135deg, var(--accentDark), var(--accent))
    color: white
    border-color: var(--accent)
    box-shadow: 0 0 px(15) rgba(33, 150, 243, 0.4)
  &:hover:not(.active)
    border-color: var(--accent)
    color: var(--textPrimary)

// 全局控制
.globalControls
  display: flex
  gap: px(16)
  margin-bottom: px(20)

.controlBtn
  flex: 1
  height: px(48)
  padding: 0 px(12)
  border-radius: px(8)
  background: var(--cardBg)
  border: px(1) solid var(--border)
  color: var(--textPrimary)
  display: flex
  align-items: center
  justify-content: center
  gap: px(8)
  cursor: pointer
  transition: all 0.3s ease
  font-size: px(16)
  &:hover
    background: var(--accentDark)
    border-color: var(--accent)
    color: #fff
  &.primary
    background: linear-gradient(135deg, var(--accentDark), var(--accent))
    border-color: var(--accent)
    color: #fff
    box-shadow: 0 0 px(10) rgba(33, 150, 243, 0.3)

// 资源监控标签
.resourceTabs
  display: flex
  border-bottom: px(1) solid var(--border)
  margin-bottom: px(16)

.tab
  padding: px(12) px(24)
  cursor: pointer
  border-bottom: px(3) solid transparent
  transition: all 0.3s ease
  color: var(--textSecondary)
  &.active
    border-bottom: px(3) solid var(--accent)
    color: var(--accent)
  &:hover:not(.active)
    color: var(--textPrimary)

// 资源监控网格
.resourceGrid
  display: grid
  grid-template-columns: repeat(4, 1fr)
  gap: px(16)
  flex: 1

.resourceCard
  background-color: var(--cardBg)
  border-radius: px(10)
  padding: px(16)
  border: px(1) solid var(--border)
  min-width: px(250)
  min-height: px(400)
  display: flex
  flex-direction: column

.resourceHeader
  display: flex
  align-items: center
  gap: px(12)
  margin-bottom: px(16)

.resourceIcon
  width: px(40)
  height: px(40)
  border-radius: 50%
  background: rgba(33, 150, 243, 0.2)
  display: flex
  align-items: center
  justify-content: center
  font-size: 1.2rem

.resourceTitle h3
  font-size: 1.1rem
  margin: 0

.resourceTitle p
  font-size: 0.9rem
  color: var(--textSecondary)
  margin: 0

.resourceStats
  display: flex
  justify-content: space-between
  margin-bottom: px(12)

.stat
  text-align: center

.statValue
  font-size: 1.2rem
  font-weight: 600

.statLabel
  font-size: 0.85rem
  color: var(--textSecondary)

.deviation
  padding: px(3) px(8)
  border-radius: px(20)
  font-size: 0.85rem
  font-weight: 500
  &.positive
    background: rgba(76, 175, 80, 0.2)
    color: var(--success)
  &.negative
    background: rgba(244, 67, 54, 0.2)
    color: var(--danger)
  &.neutral
    background: rgba(255, 152, 0, 0.2)
    color: var(--warning)

.chartContainer
  height: px(340)
  width: 100%

// 右侧面板
.metricsGrid
  display: grid
  grid-template-columns: repeat(3, 1fr)
  gap: px(16)
  margin-bottom: px(20)

.metricCard
  background-color: var(--cardBg)
  border-radius: px(10)
  padding: px(16)
  text-align: center
  border: px(1) solid var(--border)

.metricTitle
  font-size: 0.95rem
  margin-bottom: px(10)
  color: var(--textSecondary)

.metricValue
  font-size: 1.8rem
  font-weight: 700
  margin-bottom: px(5)

.metricChange
  font-size: 0.9rem
  padding: px(3) px(10)
  border-radius: px(20)
  display: inline-block

.changePositive
  background: rgba(76, 175, 80, 0.2)
  color: var(--success)

.changeNegative
  background: rgba(244, 67, 54, 0.2)
  color: var(--danger)

.metricSubtitle
  margin-top: px(5)
  font-size: 0.8rem
  color: var(--textSecondary)

// 日志和分析容器
.logAnalysisContainer
  display: flex
  flex-direction: column
  gap: px(16)
  flex: 1
  min-height: px(480)

.logContainer
  background-color: var(--cardBg)
  border-radius: px(8)
  padding: px(16)
  border: px(1) solid var(--border)
  margin-bottom: 0
  display: flex
  flex-direction: column
  min-height: px(420)
  flex: 1
  max-height: px(680)

.logHeader
  display: flex
  align-items: center
  gap: px(10)
  margin-bottom: px(16)
  font-size: 1.1rem
  font-weight: 600
  color: var(--accent)

.logList
  flex: 1
  overflow-y: auto
  display: flex
  flex-direction: column
  gap: px(12)
  padding-right: px(8)

  // 自定义滚动条样式
  &::-webkit-scrollbar
    width: px(8)

  &::-webkit-scrollbar-track
    background: rgba(255, 255, 255, 0.1)
    border-radius: px(4)

  &::-webkit-scrollbar-thumb
    background: linear-gradient(180deg, #4A90E2 0%, #357ABD 100%)
    border-radius: px(4)
    box-shadow: 0 px(2) px(4) rgba(0, 0, 0, 0.2)

    &:hover
      background: linear-gradient(180deg, #5BA0F2 0%, #4A8ACD 100%)
      box-shadow: 0 px(2) px(8) rgba(74, 144, 226, 0.4)

  &::-webkit-scrollbar-thumb:active
    background: linear-gradient(180deg, #357ABD 0%, #2E6BA8 100%)

  &::-webkit-scrollbar-corner
    background: transparent

.logEntry
  display: flex
  gap: px(10)
  padding-bottom: px(12)
  border-bottom: px(1) solid var(--border)
  &:last-child
    border-bottom: none

.logTime
  color: var(--textSecondary)
  font-size: 0.85rem
  min-width: px(60)

.logContent
  flex: 1

.logResource
  display: inline-block
  padding: px(2) px(8)
  border-radius: px(4)
  background: rgba(33, 150, 243, 0.2)
  color: var(--accent)
  font-size: 0.85rem
  margin-right: px(5)

.logText
  font-size: 1rem
  &.logSuccess
    color: var(--success)
  &.logWarning
    color: var(--warning)
  &.logNormal
    color: var(--textPrimary)

.analysisContainer
  background-color: var(--cardBg)
  border-radius: px(8)
  padding: px(16)
  border: px(1) solid var(--border)
  display: flex
  flex-direction: column
  flex: 3
  min-height: px(350)

.analysisTitle
  text-align: center
  margin-bottom: px(16)
  color: var(--accent)
  font-weight: 600
  font-size: 1.1rem

.analysisChart
  flex: 1
  height: 100%
  display: flex
  align-items: center
  justify-content: center