import CustomTitle from '@/components/CustomTitle';

import { Button, ConfigProvider, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';

import styles from './index.sass';

const defaultTimeRange = [dayjs(), dayjs()];

export default function () {
  const [form] = Form.useForm();

  return (
    <ConfigProvider
      theme={{
        token: {},
        components: {
          Form: {
            labelColor: '#fff',
          },
          DatePicker: {
            colorBgContainer: 'rgba(0,0,0,0)',
            colorBgElevated: '#0D2C5C',
            colorText: '#fff',
            colorTextHeading: '#fff',
            colorBorder: '#fff',
            colorIcon: '#fff',
            colorPrimary: 'rgba(24, 93, 158, 1)',
            cellActiveWithRangeBg: 'rgba(24, 93, 158, 1)',
            colorTextPlaceholder: '#999',
          },
        },
      }}
    >
      <div className={styles.container}>
        {/* 上半部分 */}

        {/* 右侧区域 */}
        <div className={styles.rightSection}>
          {/* 右侧面板：调度模式选择 */}

          {/* 右侧面板：执行策略信息 */}
          <div className={styles.rightBottomPanel}>
            <CustomTitle>执行策略信息</CustomTitle>

            {/* 筛选区域 */}
            <div className={styles.filterSection}>
              <Form
                layout="inline"
                form={form}
                initialValues={{
                  timeRange: defaultTimeRange,
                }}
              >
                <Form.Item name="timeRange" label="时间范围">
                  <DatePicker.RangePicker
                    allowClear={false}
                    className={styles.datePicker}
                    picker="date"
                  />
                </Form.Item>
                <Form.Item name="strategy" label="策略">
                  <Button className={styles.filterBtn}>充电</Button>
                </Form.Item>
                <Form.Item name="status" label="状态">
                  <Button className={styles.filterBtn}>放电</Button>
                </Form.Item>
                <Button className={styles.queryBtn} type="primary">
                  查询
                </Button>
                <Button className={styles.queryBtn}>重置</Button>
              </Form>
            </div>

            {/* 甘特图区域 */}
            <div className={styles.ganttContainer}>
              {/* 时间轴头部 */}
              <div className={styles.ganttHeader}>
                <div className={styles.ganttRowLabel}>任务名称</div>
                <div className={styles.ganttTimeline}>
                  <div className={styles.timeSlot}>04/02</div>
                  <div className={styles.timeSlot}>04/03</div>
                  <div className={styles.timeSlot}>04/04</div>
                  <div className={styles.timeSlot}>04/05</div>
                  <div className={styles.timeSlot}>04/06</div>
                  <div className={styles.timeSlot}>04/07</div>
                  <div className={styles.timeSlot}>04/08</div>
                  <div className={styles.timeSlot}>04/09</div>
                  <div className={styles.timeSlot}>04/10</div>
                  <div className={styles.timeSlot}>04/11</div>
                  <div className={styles.timeSlot}>04/12</div>
                </div>
              </div>

              {/* 甘特图内容 */}
              <div className={styles.ganttBody}>
                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-充电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.blueBar}
                      style={{ left: '0%', width: '100%' }}
                    >
                      宁德时代储能-充电计划 (100%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-放电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.orangeBar}
                      style={{ left: '10%', width: '80%' }}
                    >
                      宁德时代储能-放电计划 (80%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-充电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.greenBar}
                      style={{ left: '20%', width: '40%' }}
                    >
                      宁德时代储能-充电计划 (40%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-放电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.blueBar}
                      style={{ left: '30%', width: '70%' }}
                    >
                      宁德时代储能-放电计划 (70%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-充电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.orangeBar}
                      style={{ left: '15%', width: '85%' }}
                    >
                      宁德时代储能-充电计划 (85%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-充电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.greenBar}
                      style={{ left: '0%', width: '60%' }}
                    >
                      宁德时代储能-充电计划 (60%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-放电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.blueBar}
                      style={{ left: '25%', width: '50%' }}
                    >
                      宁德时代储能-放电计划 (50%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-放电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.orangeBar}
                      style={{ left: '10%', width: '90%' }}
                    >
                      宁德时代储能-放电计划 (90%)
                    </div>
                  </div>
                </div>

                <div className={styles.ganttRow}>
                  <div className={styles.ganttRowLabel}>
                    宁德时代储能-充电计划
                  </div>
                  <div className={styles.ganttTimeline}>
                    <div
                      className={styles.ganttBar + ' ' + styles.greenBar}
                      style={{ left: '5%', width: '45%' }}
                    >
                      宁德时代储能-充电计划 (45%)
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
}
