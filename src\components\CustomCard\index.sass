@import '@/utils/helpers.sass'
.metricCard
  background: rgba(16, 42, 67, 0.7)
  border-radius: px(16)
  padding: px(20)
  text-align: center
  box-shadow: 0 px(8) px(16) rgba(0, 0, 0, 0.2)
  border: 1px solid rgba(64, 128, 255, 0.2)
  transition: all 0.3s
  position: relative
  overflow: hidden

  &::before
    content: ""
    position: absolute
    top: 0
    left: 0
    width: 100%
    height: px(4)
    background: linear-gradient(90deg, #4facfe, #00f2fe)

  &:hover
    transform: translateY(px(-5))
    box-shadow: 0 px(12) px(24) rgba(0, 0, 0, 0.3)

.metricValue
  font-size: px(36)
  font-weight: 700
  margin: px(15) 0
  background: linear-gradient(90deg, #4facfe, #00f2fe)
  -webkit-background-clip: text
  background-clip: text
  -webkit-text-fill-color: transparent

.metricTitle
  font-size: px(16)
  color: #8cb3d9

.metricSubtitle
  font-size: px(14)
  color: #8cb3d9

        
      
  
  
 
      
        
        
       


