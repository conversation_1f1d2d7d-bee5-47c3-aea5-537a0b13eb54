@import '@/utils/helpers.sass'

 
:global
    .ant-table
      background-color: transparent
      color: white
    .ant-table-thead > tr > th
      background-color: rgba(255, 255, 255, 0.1)
      color: rgba(255, 255, 255, 0.9)
      border-bottom: px(1) solid rgba(255, 255, 255, 0.2)
      padding: px(8)
      text-align: center
      font-size: px(16)
    .ant-table-tbody > tr > td
      border: px(1) solid rgba(255, 255, 255, 0.2)
      padding: px(8)
      text-align: center
      font-size: px(16)
      color: white
    .ant-table-tbody > tr:nth-child(even) > td
      background-color: rgba(255, 255, 255, 0.05)
    .ant-table-tbody > tr:hover > td
      background-color: rgba(57, 118, 193, 0.547) !important 
    .ant-table-tbody > tr:nth-child(even):hover > td
      background-color: rgba(255, 255, 255, 0.05) !important
  
    .ant-pagination-item
      background-color: rgba(0, 0, 0, 0)
      border-color: rgba(255, 255, 255, 0.2)
    .ant-pagination-item-active
      background-color: rgba(57, 118, 193, 0.5)
      border-color: rgba(57, 118, 193, 0.8)
    .ant-pagination-item a
      color: white
    .ant-select-selector
      background-color: rgba(0, 0, 0, 0) !important
      border-color: rgba(255, 255, 255, 0.2) !important
      color: white
    .ant-select-arrow
      color: white
    .ant-pagination-item-link
      color: white !important
      background-color: rgb(255, 255, 255)
      border-color: rgba(255, 255, 255, 0.2)
           