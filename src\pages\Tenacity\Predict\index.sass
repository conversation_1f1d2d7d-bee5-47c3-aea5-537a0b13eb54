@import '@/utils/helpers.sass'

.container
  width: 100%
  height: px(900)
  display: flex
  flex-direction: column
  color: #e0f0ff
  overflow: hidden
  padding: px(5)
  position: relative

.glow
  position: absolute
  width: px(200)
  height: px(180)
  background: radial-gradient(circle, rgba(79, 172, 254, 0.3), transparent 70%)
  filter: blur(px(30))
  z-index: 0

.glow1
  top: 10%
  left: 10%

.glow2
  bottom: 10%
  right: 10%

.mainContent
  display: flex
  gap: px(20)
  flex: 1
  overflow: hidden
  position: relative
  z-index: 1

.leftPanel
  width: 30%
  display: flex
  flex-direction: column
  gap: px(20)
  height: 100%

.rightPanel
  flex: 1
  height: 100%

.panel
  border-radius: px(16)
  overflow: hidden
  display: flex
  flex-direction: column
  border: 1px solid rgba(64, 128, 255, 0.2)
  backdrop-filter: blur(px(10))
  box-shadow: 0 px(8) px(32) rgba(0, 0, 0, 0.3)

.panelHeader
  padding: px(18) px(25)
  background: rgba(25, 65, 100, 0.8)
  border-bottom: 1px solid rgba(64, 128, 255, 0.2)
  font-size: px(18)
  font-weight: 600
  color: #4facfe
  display: flex
  align-items: center
  gap: px(12)
  flex-shrink: 0

.panelBody
  padding: px(10)
  overflow: auto
  flex: 1

.dataGroup
  margin-bottom: px(25)

.dataImportRow
  display: flex
  gap: px(15)
  margin-bottom: px(25)

  .dataGroup
    flex: 1
    margin-bottom: 0

.dataGroupTitle
  font-size: px(16)
  font-weight: 600
  margin-bottom: px(15)
  padding-left: px(10)
  border-left: 3px solid #4facfe
  display: flex
  align-items: center
  gap: px(10)
  color: #8cb3d9

.dataItem
  display: flex
  margin-bottom: px(15)
  align-items: center
  padding: px(12) px(15)
  background: rgba(25, 65, 100, 0.4)
  border-radius: px(10)
  border: 1px solid rgba(64, 128, 255, 0.1)

.dataItemRow
  display: flex
  gap: px(15)
  margin-bottom: px(15)

.dataItemHalf
  flex: 0 0 45%
  display: flex
  align-items: center
  padding: px(10) px(12)
  background: rgba(25, 65, 100, 0.4)
  border-radius: px(10)
  border: 1px solid rgba(64, 128, 255, 0.1)

.dataItemThird
  flex: 1
  display: flex
  align-items: center
  padding: px(10) px(12)
  background: rgba(25, 65, 100, 0.4)
  border-radius: px(10)
  border: 1px solid rgba(64, 128, 255, 0.1)

.dataItemQuarter
  flex: 0 0 35%
  display: flex
  align-items: center
  padding: px(12) px(15)
  background: rgba(25, 65, 100, 0.4)
  border-radius: px(10)
  border: 1px solid rgba(64, 128, 255, 0.1)

.dataLabel
  width: px(120)
  font-size: px(14)
  color: #8cb3d9
  flex-shrink: 0

.dataItemHalf .dataLabel
  width: px(60)
  font-size: px(13)

.dataItemThird .dataLabel
  width: px(60)
  font-size: px(12)

.dataItemQuarter .dataLabel
  width: px(80)
  font-size: px(13)

.dataValue
  flex: 1
  font-weight: 500
  color: white
  font-size: px(16)

.inputField
  flex: 1
  padding: px(6) px(10)
  border: 1px solid rgba(64, 128, 255, 0.3)
  border-radius: px(6)
  background: rgba(25, 65, 100, 0.6)
  color: white
  font-size: px(13)
  outline: none
  min-width: 0

  &:focus
    border-color: #4facfe
    box-shadow: 0 0 px(10) rgba(79, 172, 254, 0.3)

.uploadArea
  border: 2px dashed rgba(64, 128, 255, 0.3)
  border-radius: px(10)
  padding: px(30) px(20)
  text-align: center
  cursor: pointer
  transition: all 0.3s
  background: rgba(25, 65, 100, 0.2)
  margin-bottom: px(15)

  &:hover
    border-color: #4facfe
    background: rgba(25, 65, 100, 0.4)

.uploadIcon
  font-size: px(48)
  color: #4facfe
  margin-bottom: px(15)

.uploadText
  color: #e0f0ff
  font-size: px(12)

.uploadHint
  font-size: px(12)
  color: #8cb3d9
  margin-top: px(8)

.dataSummary
  display: grid
  grid-template-columns: 1fr 1fr 1fr
  gap: px(15)
  margin-top: px(10)

.summaryItem
  background: rgba(25, 65, 100, 0.4)
  border-radius: px(8)
  padding: px(15)
  text-align: center

.summaryLabel
  font-size: px(12)
  color: #8cb3d9
  margin-bottom: px(8)

.summaryValue
  font-size: px(18)
  font-weight: 600
  color: #4facfe

.quickSelect
  display: flex
  gap: px(10)
  margin-top: px(15)

.quickSelectInline
  flex: 1
  display: flex
  gap: px(8)
  align-items: center

.quickBtn
  flex: 1
  padding: px(8) px(12)
  border: 1px solid rgba(64, 128, 255, 0.3)
  border-radius: px(6)
  background: rgba(25, 65, 100, 0.4)
  color: #8cb3d9
  cursor: pointer
  transition: all 0.3s
  font-size: px(12)

  &:hover
    border-color: #4facfe
    color: white

  &.active
    background: rgba(26, 107, 196, 0.5)
    color: white
    border-color: #4facfe

.btn
  flex: 1
  padding: px(16)
  border: none
  border-radius: px(10)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s
  display: flex
  align-items: center
  justify-content: center
  gap: px(10)
  font-size: px(16)
  background: rgba(25, 65, 100, 0.6)
  color: white
  border: 1px solid rgba(64, 128, 255, 0.3)

  &:hover
    transform: translateY(px(-3))
    box-shadow: 0 px(5) px(20) rgba(0, 0, 0, 0.3)

.btnPrimary
  background: linear-gradient(90deg, #1a6bc4, #0d4a96)
  box-shadow: 0 0 px(15) rgba(26, 107, 196, 0.5)

.actions
  display: flex
  gap: px(15)
  margin-top: px(20)

.resultPanel
  height: 100%
  display: flex
  flex-direction: column
  gap: px(8)
  overflow: auto

.metrics
  display: grid
  grid-template-columns: repeat(4, 1fr)
  gap: px(10)

.metricCard
  border-radius: px(12)
  padding: px(15)
  text-align: center
  box-shadow: 0 px(6) px(12) rgba(0, 0, 0, 0.2)
  border: 1px solid rgba(64, 128, 255, 0.2)
  transition: all 0.3s
  position: relative
  overflow: hidden

  &::before
    content: ""
    position: absolute
    top: 0
    left: 0
    width: 100%
    height: px(4)
    background: linear-gradient(90deg, #4facfe, #00f2fe)

  &:hover
    transform: translateY(px(-5))
    box-shadow: 0 px(12) px(24) rgba(0, 0, 0, 0.3)

.metricValue
  font-size: px(28)
  font-weight: 700
  margin: px(10) 0
  background: linear-gradient(90deg, #4facfe, #00f2fe)
  -webkit-background-clip: text
  background-clip: text
  -webkit-text-fill-color: transparent

.metricTitle
  font-size: px(14)
  color: #8cb3d9

.metricSubtitle
  font-size: px(12)
  color: #8cb3d9

.chartContainer
  flex: 1
  border-radius: px(12)
  padding: px(15)
  border: 1px solid rgba(64, 128, 255, 0.2)
  display: flex
  flex-direction: column
  min-height: 0

.chartHeader
  display: flex
  justify-content: space-between
  margin-bottom: px(10)
  align-items: center
  flex-shrink: 0

.chartTitle
  font-size: px(18)
  font-weight: 600
  color: #4facfe
  display: flex
  align-items: center
  gap: px(10)

.chartControls
  display: flex
  gap: px(10)

.chartBtn
  padding: px(8) px(16)
  border: 1px solid rgba(64, 128, 255, 0.3)
  border-radius: px(6)
  background: rgba(25, 65, 100, 0.4)
  color: #8cb3d9
  cursor: pointer
  transition: all 0.3s
  font-size: px(14)

  &.active
    background: rgba(26, 107, 196, 0.5)
    color: white
    border-color: #4facfe

.chartContent
  flex: 1
  display: flex

.predictionDetails
  display: grid
  grid-template-columns: 1fr 1fr
  gap: px(20)

.detailCard
  border-radius: px(12)
  padding: px(15)
  border: 1px solid rgba(64, 128, 255, 0.2)

.detailHeader
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(10)

.detailTitle
  font-size: px(18)
  font-weight: 600
  color: #4facfe

.detailStatus
  padding: px(6) px(12)
  border-radius: px(12)
  font-size: px(12)
  font-weight: 600

  &.success
    background: rgba(76, 175, 80, 0.2)
    color: #4caf50

  &.warning
    background: rgba(255, 193, 7, 0.2)
    color: #ffc107

.detailContent
  display: flex
  flex-direction: column
  gap: px(10)

.detailRow
  display: flex
  gap: px(15)

.detailItem
  flex: 1
  display: flex
  justify-content: space-between
  padding: px(8) px(10)
  background: rgba(25, 65, 100, 0.3)
  border-radius: px(6)
  border: 1px solid rgba(64, 128, 255, 0.1)
  font-size: px(14)

.recommendations
  border: 1px solid rgba(76, 175, 80, 0.3)
  border-radius: px(12)
  padding: px(12)
  flex-shrink: 0

.recommendationsHeader
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(10)

.recommendationsTitle
  font-weight: 600
  color: #4caf50
  display: flex
  align-items: center
  gap: px(10)
  font-size: px(16)
.recommendationsContent
  display: flex
  width: 100%
  justify-content: space-between
  align-items: center
.scenarioBtn
  padding: px(16)
  border: none
  border-radius: px(10)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s
  display: flex
  align-items: center
  justify-content: center
  gap: px(10)
  font-size: px(16)
  color: white
  background: linear-gradient(90deg, #1a6bc4, #0d4a96)
  box-shadow: 0 0 px(15) rgba(26, 107, 196, 0.5)

  &:hover
    transform: translateY(px(-3))
    box-shadow: 0 px(5) px(20) rgba(0, 0, 0, 0.3)

  i
    font-size: px(16)

.recommendationsList
  padding-left: px(20)
  font-size: px(14)

  li
    margin-bottom: px(8)
    line-height: 1.5
    color: #e0f0ff

.pulse
  animation: pulse 1.5s infinite

@keyframes pulse
  0%
    opacity: 0.7
  50%
    opacity: 1
  100%
    opacity: 0.7
