import CustomTable from '@/components/CustomTable';
import { AGetUnusual } from '@/services/datalist';
import { px } from '@/utils/util';
import { useAntdTable } from 'ahooks';
import { Button, ConfigProvider, DatePicker, Form, Input, Select } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import styles from './index.sass';
const defaultTimeRange = [dayjs(), dayjs()];
export default function () {
  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData: any,
  ) => {
    const { pageSize, current } = params;
    const {
      timeRange = defaultTimeRange,
      type = 'unit',
      rank,
      name,
      company,
    } = formData;
    return AGetUnusual({
      name,
      company,
      rank,
      pageSize,
      current,
      type,
      startTime: timeRange[0].format('YYYY-MM-DD'),
      endTime: timeRange[1].format('YYYY-MM-DD'),
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    defaultParams: [
      {
        current: 1,
        pageSize: 15,
      },
    ],
    form,
    defaultType: 'advance',
  });
  const { submit } = search;
  const columns: ColumnsType<any> = [
    {
      title: '评估记录名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '数据量',
      dataIndex: 'power',
      key: 'power',
      align: 'center',
    },
    {
      title: '执行进度',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
    },
    {
      title: '评估时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
      width: px(250),
      render: () => {
        return (
          <div className={styles.operate}>
            <div>详情</div>
            <div>编辑</div>
            <div>删除</div>
          </div>
        );
      },
    },
  ];
  return (
    <ConfigProvider
      theme={{
        token: {},
        components: {
          Tag: {
            colorTextLightSolid: '#000',
          },
          Form: {
            labelColor: '#fff',
          },
        },
      }}
    >
      <div className={styles.box}>
        <div className={styles.filter}>
          <Form
            layout="inline"
            form={form}
            initialValues={{
              timeRange: defaultTimeRange,
            }}
          >
            <Form.Item name={'type'} label="状态">
              <Select
                defaultValue={'unit'}
                className={styles.select}
                options={[
                  {
                    label: '发电侧',
                    value: 'unit',
                  },
                  {
                    label: '用户侧',
                    value: 'user',
                  },
                ]}
              ></Select>
            </Form.Item>
            <Form.Item name="name" label="名称">
              <Input></Input>
            </Form.Item>
            <Form.Item name="timeRange" label="时间范围">
              <DatePicker.RangePicker
                allowClear={false}
                className={styles.date}
                picker="date"
              />
            </Form.Item>
            <Button className={styles.button} type="primary" onClick={submit}>
              查询
            </Button>
            <Button className={styles.button} type="primary">
              新增韧性评估记录
            </Button>
          </Form>
        </div>
        <CustomTable columns={columns} {...tableProps}></CustomTable>
      </div>
    </ConfigProvider>
  );
}
