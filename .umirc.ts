import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  esbuildMinifyIIFE: true,
  jsMinifierOptions: {
    target: ['chrome80', 'es2020'],
  },
  proxy: {
    '/api': {
      target: 'http://**************:61106',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
  },
  plugins: [require.resolve('@umijs/plugins/dist/unocss')],
  // openAPI: {
  //   requestLibPath: "import { request } from '@umijs/max'",
  //   // 或者使用在线的版本
  //   // schemaPath: "https://gw.alipayobjects.com/os/antfincdn/M%24jrzTTYJN/oneapi.json",
  //   schemaPath: 'http://**************:11101/v2/api-docs?group=webApi',
  //   projectName: 'decisionSupport',
  // },
  unocss: {
    // 检测 className 的文件范围，若项目不包含 src 目录，可使用 `pages/**/*.tsx`
    watch: ['src/**/*.tsx'],
  },
  layout: {
    title: '@umijs/max',
    layout: 'topmenu',
  },
  routes: [
    {
      path: '/',
      redirect: '/home',
      layout: false,
    },
    {
      name: '首页',
      path: '/home',
      routes: [
        {
          name: '电力分项计量',
          path: '/home/<USER>',
          component: './PowerInfo',
        },
        {
          name: '多能流信息',
          path: '/home/<USER>',
          component: './MultiEnergy',
        },
        {
          name: '园区碳流',
          path: '/home/<USER>',
          component: './CarbonFlow',
        },
        {
          name: '碳计量',
          path: '/home/<USER>',
          component: './CarbonAccount',
        },
        {
          name: '碳看板',
          path: '/home/<USER>',
          component: './CarbonBoard',
        },
      ],
    },
    {
      name: '市场',
      path: '/market',
      component: './Market',
    },
    {
      name: '韧性',
      path: '/tenacity',
      component: './Tenacity',
    },
  ],
  npmClient: 'pnpm',
  chainWebpack(memo) {
    memo.output.globalObject('self');
  },
});
