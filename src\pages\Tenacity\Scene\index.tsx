import CustomCard from '@/components/CustomCard';
import CustomTitle from '@/components/CustomTitle';
import HeatMap from './HeatMap';
import styles from './index.sass';

export default function () {
  return (
    <div className={styles.gridLayout}>
      <div className={styles.gridItem}>
        <CustomTitle>评估输入数据</CustomTitle>

        {/* 场景数据 */}
        <div className={styles.sectionContainer}>
          <div className={styles.sectionTitle}>🌪️ 场景数据</div>
          <div className={styles.dataRow}>
            <span className={styles.label}>场景类型</span>
            <span className={styles.value}>台风场景(12级)</span>
          </div>
          <div className={styles.dataRow}>
            <span className={styles.label}>持续时间</span>
            <span className={styles.value}>24小时</span>
          </div>
          <div className={styles.dataRow}>
            <span className={styles.label}>影响范围</span>
            <span className={styles.value}>整个园区</span>
          </div>
        </div>

        {/* 实时监测数据 */}
        <div className={styles.sectionContainer}>
          <div className={styles.sectionTitle}>📊 实时监测数据</div>
          <div className={styles.dataRow}>
            <span className={styles.label}>负荷水平</span>
            <span className={styles.value}>85.2 MW</span>
          </div>
          <div className={styles.dataRow}>
            <span className={styles.label}>光伏出力</span>
            <span className={styles.value}>32.5 MW</span>
          </div>
          <div className={styles.dataRow}>
            <span className={styles.label}>风电出力</span>
            <span className={styles.value}>18.7 MW</span>
          </div>
          <div className={styles.dataRow}>
            <span className={styles.label}>可调资源出力</span>
            <span className={styles.value}>30.2 MW</span>
          </div>
        </div>

        {/* 设备条件模拟 */}
        <div className={styles.sectionContainer}>
          <div className={styles.sectionTitle}>⚙️ 设备条件模拟</div>
          <div className={styles.checkboxContainer}>
            <div className={styles.checkboxRow}>
              <span className={styles.checkboxIcon}>⚫</span>
              <span className={styles.checkboxLabel}>重要设备故障</span>
            </div>
            <div className={styles.checkboxRow}>
              <span className={styles.checkboxIcon}>⚫</span>
              <span className={styles.checkboxLabel}>增加负荷波动</span>
            </div>
          </div>
          <div className={styles.selectContainer}>
            <span className={styles.selectLabel}>台风模式</span>
          </div>

          <div className={styles.buttonContainer}>
            <button type="button" className={styles.startButton}>
              ▶ 开始评估
            </button>
            <button type="button" className={styles.saveButton}>
              💾 保存配置
            </button>
          </div>
        </div>
      </div>
      <div className={styles.gridItem}>
        <CustomTitle>韧性评估结果</CustomTitle>
        <div className={styles.cardContainer}>
          <CustomCard
            title="供电韧性"
            value={82.5}
            unit="%"
            subText="评估通过"
          />
          <CustomCard title="供气韧性" value={76.3} unit="%" subText="需关注" />
          <CustomCard title="综合指数" value={79.4} subText="中等水平" />
          <CustomCard title="薄弱环节" value={3} subText="需优先处理" />
        </div>
        <HeatMap />

        {/* 韧性评估详细信息 */}
        <div className={styles.assessmentDetails}>
          {/* 关键区域状态列和3号储气罐 */}
          <div className={styles.statusContainer}>
            <div className={styles.statusColumn}>
              <div className={styles.statusHeader}>
                <span className={styles.statusTitle}>关键区域状态列</span>
                <span className={styles.statusPercentage}>路网指数: 92%</span>
              </div>
              <div className={styles.statusList}>
                <div className={styles.statusItem}>
                  <span className={styles.bullet}>•</span>
                  <span>重要子网区域，缺乏应急保护</span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.bullet}>•</span>
                  <span>无备用线路，单点故障风险高</span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.bullet}>•</span>
                  <span>设备老化严重，故障率高于平均水平</span>
                </div>
              </div>
            </div>

            <div className={styles.statusColumn}>
              <div className={styles.statusHeader}>
                <span className={styles.statusTitle}>3号储气罐</span>
                <span className={styles.statusPercentage}>储气指数: 87%</span>
              </div>
              <div className={styles.statusList}>
                <div className={styles.statusItem}>
                  <span className={styles.bullet}>•</span>
                  <span>防护等级不足，无法抵御12级台风</span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.bullet}>•</span>
                  <span>影响下游12个负荷点，影响范围广</span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.bullet}>•</span>
                  <span>维护记录显示已连续6个月未检修</span>
                </div>
              </div>
            </div>
          </div>

          {/* 优化建议措施 */}
          <div className={styles.optimizationContainer}>
            <div className={styles.optimizationHeader}>
              <div className={styles.optimizationTitleGroup}>
                <span className={styles.optimizationIcon}>💡</span>
                <span className={styles.optimizationTitle}>优化建议措施</span>
              </div>
              <button type="button" className={styles.executeButton}>
                ⚡ 执行韧性调度
              </button>
            </div>
            <div className={styles.optimizationList}>
              <div className={styles.optimizationItem}>
                <span className={styles.optimizationBullet}>•</span>
                <span>
                  启动储电系统备用方案，在台风登陆前6小时启用备用电源系统
                </span>
              </div>
              <div className={styles.optimizationItem}>
                <span className={styles.optimizationBullet}>•</span>
                <span>
                  加固东南区域防护设施，为在役储罐增加防风网，预算约15万元
                </span>
              </div>
              <div className={styles.optimizationItem}>
                <span className={styles.optimizationBullet}>•</span>
                <span>
                  调整负荷分配优先级，确保医院、指挥中心等关键设施供电
                </span>
              </div>
              <div className={styles.optimizationItem}>
                <span className={styles.optimizationBullet}>•</span>
                <span>储气罐紧急处理，在24小时内完成3号储气罐防护加固工作</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
