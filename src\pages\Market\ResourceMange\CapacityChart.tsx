import '@/utils/helpers.sass';
import { px } from '@/utils/util';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';

const Price: React.FC = () => {
  const chartRef = useRef(null);
  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    const chartInstance = echarts.init(chartRef.current);
    let max = 100;
    let value = 32;
    let rate = Math.round((value * 100) / max);
    // 配置项和数据
    const options = {
      title: [
        {
          text: '{b|' + rate + '%}',
          show: true,
          x: 'center',
          y: 'center',
          textStyle: {
            rich: {
              b: {
                fontSize: px(48),
                color: '#66FFFF',
                fontFamily: 'youshe',
              },
            },
          },
        },
      ],
      polar: {
        center: ['50%', '50%'],
        radius: ['60%', '75%'],
      },
      angleAxis: {
        max: max,
        show: false,
      },
      radiusAxis: {
        type: 'category',
        show: true,
        axisLabel: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: '',
          type: 'bar',
          roundCap: true,
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(19, 84, 146, .4)',
          },
          data: [value],
          coordinateSystem: 'polar',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: '#005DCF',
                },
                {
                  offset: 1,
                  color: '#00CCFF',
                },
              ]),
            },
          },
        },
        {
          name: '',
          type: 'gauge',
          radius: '54%',
          axisLine: {
            lineStyle: {
              color: [
                [
                  1,
                  new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: 'rgba(0, 182, 253, 0)',
                    },
                    {
                      offset: 0.5,
                      color: 'rgba(0, 182, 253, .2)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 182, 253, .4)',
                    },
                  ]),
                ],
              ],
              width: 1,
            },
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          itemStyle: {
            show: false,
          },
          detail: {
            show: false,
          },
          data: [],
          pointer: {
            show: false,
          },
        },
      ],
    };
    chartInstance.setOption(options);
  }, []);

  return <div ref={chartRef} style={{ width: px(350), height: px(320) }} />;
};

export default Price;
