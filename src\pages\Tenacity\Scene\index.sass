@import '@/utils/helpers.sass'
.gridLayout
  width: 100%
  height: 100%
  flex: 1
  display: grid
  grid-template-columns: 1fr 2fr
  grid-template-rows: 1fr
  gap: px(10)

.gridItem
  display: flex
  flex-direction: column
  height: 100%

  // 左侧评估输入数据的特殊布局
  &:first-child
    .sectionContainer:last-child
      flex: 1
      display: flex
      flex-direction: column
      justify-content: space-between

.cardContainer
  display: flex
  gap: px(15)
  justify-content: space-between
  align-items: stretch
  width: 100%

  > *
    flex: 1

// 评估输入数据样式
.sectionContainer
  margin-bottom: px(10)
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  padding: px(18)
  border: 1px solid rgba(255, 255, 255, 0.1)

  // 最后一个section容器填满剩余空间
  &:last-child
    margin-bottom: 0
    flex: 1
    display: flex
    flex-direction: column

.sectionTitle
  color: #fff
  font-size: px(16)
  font-weight: bold
  margin-bottom: px(12)
  display: flex
  align-items: center
  gap: px(8)

.dataRow
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(8)
  padding: px(8) px(12)
  background: rgba(0, 0, 0, 0.2)
  border-radius: px(4)

.label
  color: rgba(255, 255, 255, 0.8)
  font-size: px(14)

.value
  color: #fff
  font-size: px(14)
  font-weight: 500

.checkboxContainer
  flex: 1
  display: flex
  flex-direction: column
  justify-content: center

.checkboxRow
  display: flex
  align-items: center
  margin-bottom: px(15)
  padding: px(12)
  background: rgba(0, 0, 0, 0.1)
  border-radius: px(6)

.checkboxIcon
  color: #4CAF50
  margin-right: px(8)
  font-size: px(12)

.checkboxLabel
  color: #fff
  font-size: px(14)

.selectContainer
  margin-bottom: px(20)
  padding: px(12) px(15)
  background: rgba(0, 0, 0, 0.2)
  border-radius: px(6)
  border: 1px solid rgba(255, 255, 255, 0.2)

.selectLabel
  color: #fff
  font-size: px(14)

.buttonContainer
  display: flex
  gap: px(10)

.startButton, .saveButton
  flex: 1
  padding: px(15) px(20)
  border: none
  border-radius: px(8)
  font-size: px(15)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s ease

.startButton
  background: linear-gradient(135deg, #4CAF50, #45a049)
  color: #fff

  &:hover
    background: linear-gradient(135deg, #45a049, #3d8b40)

.saveButton
  background: linear-gradient(135deg, #2196F3, #1976D2)
  color: #fff

  &:hover
    background: linear-gradient(135deg, #1976D2, #1565C0)

// 占位区域样式
.placeholderArea
  flex: 1
  display: flex
  flex-direction: column
  margin-top: px(20)

.placeholderContent
  flex: 1
  background: rgba(255, 255, 255, 0.03)
  border-radius: px(8)
  padding: px(30)
  border: 1px solid rgba(255, 255, 255, 0.08)
  display: flex
  align-items: center
  justify-content: center
  min-height: px(400)

.placeholderText
  color: rgba(255, 255, 255, 0.5)
  font-size: px(14)
  text-align: center
  font-style: italic

// 韧性评估详细信息样式
.assessmentDetails
  margin-top: px(10)

.statusContainer
  display: flex
  gap: px(15)
  margin-bottom: px(10)

.statusColumn
  flex: 1
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  padding: px(15)
  border: 1px solid rgba(255, 255, 255, 0.1)

.statusHeader
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(12)
  padding-bottom: px(8)
  border-bottom: 1px solid rgba(255, 255, 255, 0.1)

.statusTitle
  color: #fff
  font-size: px(16)
  font-weight: bold

.statusPercentage
  color: #FF6B6B
  font-size: px(14)
  font-weight: 500

.statusList
  display: flex
  flex-direction: column
  gap: px(8)

.statusItem
  display: flex
  align-items: flex-start
  gap: px(8)
  color: rgba(255, 255, 255, 0.9)
  font-size: px(14)
  line-height: 1.4

.bullet
  color: #4CAF50
  font-weight: bold
  margin-top: px(2)

.optimizationContainer
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  padding: px(15)
  border: 1px solid rgba(255, 255, 255, 0.1)

.optimizationHeader
  display: flex
  align-items: center
  justify-content: space-between
  margin-bottom: px(12)
  padding-bottom: px(8)
  border-bottom: 1px solid rgba(255, 255, 255, 0.1)

.optimizationTitleGroup
  display: flex
  align-items: center
  gap: px(8)

.optimizationIcon
  font-size: px(18)

.optimizationTitle
  color: #fff
  font-size: px(16)
  font-weight: bold

.optimizationList
  display: flex
  flex-direction: column
  gap: px(10)

.optimizationItem
  display: flex
  align-items: flex-start
  gap: px(8)
  color: rgba(255, 255, 255, 0.9)
  font-size: px(14)
  line-height: 1.5

.optimizationBullet
  color: #FFC107
  font-weight: bold
  margin-top: px(2)

// 执行韧性调度按钮样式
.executeButton
  padding: px(10) px(20)
  border: none
  border-radius: px(6)
  font-size: px(14)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s ease
  background: linear-gradient(135deg, #2196F3, #1976D2)
  color: #fff
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3)

  &:hover
    background: linear-gradient(135deg, #1976D2, #1565C0)
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4)
    transform: translateY(-1px)

  &:active
    transform: translateY(0)
    box-shadow: 0 1px 4px rgba(33, 150, 243, 0.3)