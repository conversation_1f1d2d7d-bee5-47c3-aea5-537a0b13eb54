import { px } from '@/utils/util';
import * as echarts from 'echarts';

// 生成模拟数据
let data = [];
for (let i = 0; i < 9; i++) {
  // 修改为6个小时
  data.push({
    time: i * 4 + ':00', // 每4小时一个点，覆盖0-20小时
    charge: (Math.random() * 50 + 100).toFixed(0),
    discharge: (Math.random() * 50 + 80).toFixed(0),
  });
}

export const option = () => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['充电量', '放电量'],
      textStyle: {
        color: '#fff',
        fontSize: px(12),
      },
      top: 0,
    },
    grid: {
      top: px(30),
      left: px(20),
      right: px(20),
      bottom: px(0),
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        fontSize: px(12),
        color: '#fff',
      },
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
    },
    yAxis: {
      type: 'value',
      name: 'kW·h',
      nameTextStyle: {
        color: '#fff',
        fontSize: px(12),
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255,255,255,0.1)',
        },
      },
      axisLabel: {
        fontSize: px(12),
        color: '#fff',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
    },
    barCategoryGap: '50%', // 类目间柱子的间距
    barGap: '30%', // 同一类目下柱子的间距
    series: [
      {
        name: '充电量',
        type: 'bar',
        barWidth: px(15),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgb(16, 250, 253)' },
            { offset: 1, color: 'rgba(16, 250, 253, 0.1)' },
          ]),
        },
        data: data.map((item) => item.charge),
      },
      {
        name: '放电量',
        type: 'bar',
        barWidth: px(15),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgb(24, 141, 255)' },
            { offset: 1, color: 'rgba(24, 141, 255, 0.1)' },
          ]),
        },
        data: data.map((item) => item.discharge),
      },
    ],
  };
};
