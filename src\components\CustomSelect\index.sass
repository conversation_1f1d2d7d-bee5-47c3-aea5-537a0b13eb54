@import '@/utils/helpers.sass'
.box
 
  :global(.ant-select-selector)
    background: rgba(7, 19, 59, 0.7) !important
    border: px(1) solid rgba(65, 167, 250, 0.5) !important
    box-shadow: 0 0 px(8) rgba(65, 167, 250, 0.4) !important
  :global(.ant-select-selection-item)
    color: white !important
  :global(.ant-select-arrow)
    color: rgba(65, 167, 250, 0.9) !important
  :global(.ant-select-dropdown)
    background: rgba(81, 178, 202, 0.9) !important
  :global(.ant-select-item)
    color: white !important
    background: transparent !important
    &:hover
      background: rgba(65, 167, 250, 0.2) !important
  :global(.ant-select-item-option-selected)
    background: rgba(89, 177, 250, 0.525) !important