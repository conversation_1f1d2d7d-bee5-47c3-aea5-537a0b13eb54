@import '@/utils/helpers.sass'

.box
  width: px(500)
  height: px(250)

.infoChartContainer
  display: flex
  flex-direction: column
  width: 100%
  height: 100%
  gap: px(10)

.top
  display: flex
  padding: 0 px(20)
   

.selectContainer
  width: px(170)
 

.datePickerContainer
  color: white
  width: px(300)
  margin-left:px(10)
 

.chartContainer
  height: 100%
  width: 100%
.tabs
  height: px(45)
  margin-top: px(5)
  display: flex
  div
    background-image: url(/tabs.png)
    background-repeat: no-repeat
    background-size: 100% 100%
    color: white
    font-size: px(18)
    font-weight: bold
    width: px(500)
    display: flex
    align-items: center
    justify-content: center
    margin: 0 px(8)
    margin-left: 0
    cursor: pointer
    span
      color: #1FD6F9
    &:hover
      background-image: url(/acttabs.png)

  .active
    background-image: url(/acttabs.png)
    
  .active-1
    background-image: url(/acttabs.png)
    box-shadow: 0 0 px(10) px(4) #1978E5
    span
      color: #1978E5
      text-shadow: 0 0 px(5) rgba(25, 120, 229, 0.8)
    
  .active-2f
    background-image: url(/acttabs.png)
    box-shadow: 0 0 px(10) px(4) #00faed
    span
      color: #00faed
      text-shadow: 0 0 px(5) rgba(0, 250, 237, 0.8)
    
  .active-2r
    background-image: url(/acttabs.png)
    box-shadow: 0 0 px(10) px(4) #5AD8A6
    span
      color: #5AD8A6
      text-shadow: 0 0 px(5) rgba(90, 216, 166, 0.8)
 