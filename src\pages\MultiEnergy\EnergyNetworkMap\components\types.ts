/**
 * 共享类型定义文件
 * 包含所有组件共用的接口和类型定义
 */

// 故障信息接口
export interface FaultInfo {
  level: 1 | 2 | 3; // 故障等级：1-严重，2-中等，3-轻微
  title: string; // 响应级别标题
  occurTime: string; // 发生时间
  description: string; // 故障描述
  location: string; // 故障位置
  currentStatus: '处理中' | '待处理' | '已解决'; // 当前状态
}

// 管道数据接口
export interface PipelineData {
  id: string;
  type: 'heat' | 'gas';
  coordinates: [number, number][];
  flow: number;
  direction: 'forward' | 'backward';
  status: 'normal' | 'warning' | 'error';
  faultInfo?: FaultInfo; // 故障信息
}

// 设施数据接口
export interface FacilityData {
  id: string;
  type:
    | 'power_plant'
    | 'substation'
    | 'storage'
    | 'heat_user'
    | 'gas_user'
    | 'industrial_user';
  coordinates: [number, number];
  name: string;
  status: 'running' | 'stopped' | 'maintenance' | 'error' | 'warning';
  capacity?: number;
  currentOutput?: number;
  faultInfo?: FaultInfo; // 故障信息
}

// 悬浮弹窗状态接口
export interface HoverPopupState {
  visible: boolean;
  x: number;
  y: number;
  faultInfo: FaultInfo | null;
}

// 实时数据状态接口
export interface RealTimeData {
  pipelines: PipelineData[];
  facilities: FacilityData[];
}

// 主组件Props接口
export interface EnergyNetworkMapProps {
  className?: string;
}
