import { px } from '@/utils/util';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
const Monitor: React.FC = () => {
  const chartRef = useRef(null);
  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    const chartInstance = echarts.init(chartRef.current);
    const color = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      {
        offset: 0,
        color: '#5CF9FE',
      },
      {
        offset: 0.17,
        color: '#468EFD',
      },
      {
        offset: 0.9,
        color: '#468EFD',
      },
      {
        offset: 1,
        color: '#5CF9FE',
      },
    ]);

    const colorSet = [[1, color]];

    const rich = {
      value: {
        fontSize: px(19),
        fontFamily: 'DINBold',
        color: '#fff',
        fontWeight: '700',
        padding: [0, 0, 0, 0],
      },
    };
    // 配置项和数据
    const options = {
      tooltip: {
        formatter: '{b} : {c}kW',
      },
      series: [
        {
          type: 'gauge',
          name: '外层辅助',
          radius: '84%',
          startAngle: '225',
          endAngle: '-45',
          splitNumber: '120',
          pointer: {
            show: false,
          },
          detail: {
            show: false,
          },
          data: [
            {
              value: 1,
            },
          ],
          title: {
            show: true,
            offsetCenter: [0, px(38)],
            textStyle: {
              color: '#fff',
              fontStyle: 'normal',
              fontWeight: 'normal',
              fontFamily: '微软雅黑',
              fontSize: px(25),
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: [[1, '#00FFFF']],
              width: px(1.2),
              opacity: 1,
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            length: px(25),
            lineStyle: {
              color: '#051932',
              width: 0,
              type: 'solid',
            },
          },
          axisLabel: {
            show: false,
          },
        },
        {
          type: 'gauge',
          radius: '82%',
          startAngle: '225',
          endAngle: '-45',
          min: 0,
          pointer: {
            show: true,
          },
          detail: {
            formatter: function (value: number) {
              return '{value|' + value.toFixed(2) + 'kW}';
            },
            rich: rich,
            offsetCenter: ['0%', '95%'],
            textStyle: {
              fontWeight: 'bold',
              fontSize: px(19),
            },
          },
          data: [
            {
              value: 100,
              name: '',
            },
          ],
          title: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: colorSet,
              width: px(10),
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              opacity: 1,
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
            length: px(31.25),
            lineStyle: {
              color: '#00377a',
              width: px(2.5),
              type: 'solid',
            },
          },
          axisLabel: {
            show: false,
          },
          animationDuration: 4000,
        },
        {
          name: '灰色内圈',
          type: 'gauge',
          z: 2,
          radius: '70%',
          startAngle: '225',
          endAngle: '-45',
          axisLine: {
            lineStyle: {
              color: [[1, '#018DFF']],
              fontSize: px(15),
              width: px(1.2),
              opacity: 1,
            },
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          pointer: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          detail: {
            show: 0,
          },
        },
        {
          name: '白色圈刻度',
          type: 'gauge',
          radius: '70%',
          startAngle: 225,
          endAngle: -45,
          min: 0,
          splitNumber: 6,
          axisTick: {
            show: false,
          },
          splitLine: {
            length: px(19),
            lineStyle: {
              width: px(2),
              color: '#018DFF',
            },
          },
          axisLabel: {
            show: true,
            distance: px(-30),
            color: 'rgba(255,255,255,0.8)',
            fontSize: px(16),
            formatter: function (v: number) {
              return Math.round(v);
            },
          },
          pointer: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              opacity: 0,
            },
          },
          detail: {
            show: false,
          },
          data: [
            {
              value: 0,
              name: '',
            },
          ],
        },
        {
          type: 'pie',
          radius: '42%',
          center: ['50%', '50%'],
          z: 1,
          itemStyle: {
            normal: {
              color: new echarts.graphic.RadialGradient(
                0.5,
                0.5,
                0.8,
                [
                  {
                    offset: 0,
                    color: '#4978EC',
                  },
                  {
                    offset: 0.5,
                    color: '#1E2B57',
                  },
                  {
                    offset: 1,
                    color: '#141F3D',
                  },
                ],
                false,
              ),
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          hoverAnimation: false,
          label: {
            show: false,
          },
          tooltip: {
            show: false,
          },
          data: [100],
          animationType: 'scale',
        },
        {
          type: 'pie',
          radius: '88%',
          center: ['50%', '50%'],
          z: 0,
          itemStyle: {
            color: 'rgba(20, 28, 51, 0.3)',
          },
          hoverAnimation: false,
          label: {
            show: false,
          },
          tooltip: {
            show: false,
          },
          data: [600],
          animationType: 'scale',
        },
      ],
    };
    chartInstance.setOption(options);
  }, []);

  return (
    <div className={styles.monitor}>
      <div ref={chartRef} style={{ width: px(350), height: px(320) }} />
    </div>
  );
};

export default Monitor;
