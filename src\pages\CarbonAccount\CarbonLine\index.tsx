import CustomButton from '@/components/CustomButton';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { option } from './option';
const Index = () => {
  const myChart = useRef<any>();
  const container = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }
    if (myChart.current) {
      myChart.current.setOption(option(), true);
    }
  }, []);

  return (
    <>
      <div className={styles.buttons}>
        <CustomButton className={styles.compute}>反演计算</CustomButton>
        <CustomButton>查看计算结果</CustomButton>
      </div>
      <div className={styles.box} ref={container}></div>
    </>
  );
};

export default Index;
