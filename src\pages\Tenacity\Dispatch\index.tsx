import { px } from '@/utils/util';
import { Button } from 'antd';
import classNames from 'classnames';
import ReactECharts from 'echarts-for-react';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';
interface LogEntry {
  time: string;
  resource: string;
  content: string;
  type: 'success' | 'warning' | 'normal';
}

const EnergyDispatchPlatform: React.FC = () => {
  const [activeTab, setActiveTab] = useState('储电系统');
  const [activePhase, setActivePhase] = useState('事故前预防');
  // const [currentTime, setCurrentTime] = useState(new Date());

  const logEntries: LogEntry[] = [
    {
      time: '09:25:42',
      resource: '储电系统',
      content: '启动计划P2放电程序，实际出力达成率98.7%',
      type: 'success',
    },
    {
      time: '09:15:18',
      resource: '储热系统',
      content: '超计划出力3.2%，触发流量调节阀自动限流',
      type: 'warning',
    },
    {
      time: '09:05:33',
      resource: '可中断负荷',
      content: '切除非关键负荷90kW，偏差率-5.3%',
      type: 'success',
    },
    {
      time: '08:55:07',
      resource: '储气系统',
      content: '供气压力稳定，实际出力325m³/h',
      type: 'success',
    },
    {
      time: '08:45:52',
      resource: '风力机组',
      content: '监测到出力下降35%，启动备用电源',
      type: 'warning',
    },
    {
      time: '08:30:15',
      resource: '主控系统',
      content: '调度执行启动，进入事故前预防阶段',
      type: 'normal',
    },
    {
      time: '08:20:30',
      resource: '光伏系统',
      content: '受云层影响，出力下降42%',
      type: 'warning',
    },
    {
      time: '08:10:05',
      resource: '调度系统',
      content: '成功导入多阶段调度计划',
      type: 'normal',
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      // setCurrentTime(new Date());
      console.log(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const tabs = ['储电系统', '储热系统', '储气系统', '可中断负荷'];
  const phases = ['事故前预防', '事故中应急', '事故后恢复'];

  // ECharts配置
  const getLineChartOption = (
    data1: number[],
    data2: number[],
    color1: string,
    color2: string,
  ) => ({
    grid: {
      top: 20,
      left: 30,
      right: 30,
      bottom: 30,
    },
    xAxis: {
      type: 'category',
      data: ['08:00', '08:30', '09:00', '09:30', '10:00', '10:30'],
      axisLine: { lineStyle: { color: '#b0b0b0' } },
      axisTick: { show: false },
      axisLabel: { color: '#b0b0b0', fontSize: 10 },
    },
    yAxis: {
      type: 'value',
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#b0b0b0', fontSize: 10 },
    },
    series: [
      {
        name: '计划出力',
        type: 'line',
        data: data1,
        lineStyle: { color: color1, width: 2 },
        itemStyle: { color: color1 },
        areaStyle: { opacity: 0.1, color: color1 },
        smooth: true,
        symbol: 'none',
      },
      {
        name: '实际出力',
        type: 'line',
        data: data2,
        lineStyle: { color: color2, width: 2, type: 'dashed' },
        itemStyle: { color: color2 },
        smooth: true,
        symbolSize: 4,
      },
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: { color: '#fff', fontSize: 12 },
    },
  });

  // const doughnutOption = {
  //   tooltip: {
  //     trigger: 'item',
  //     backgroundColor: 'rgba(0, 0, 0, 0.8)',
  //     borderColor: 'transparent',
  //     textStyle: { color: '#fff' },
  //   },
  //   legend: {
  //     orient: 'vertical',
  //     right: '5%',
  //     top: 'center',
  //     textStyle: { color: '#e0e0e0', fontSize: 12 },
  //   },
  //   series: [
  //     {
  //       type: 'pie',
  //       radius: ['40%', '70%'],
  //       center: ['35%', '50%'],
  //       data: [
  //         { value: 45, name: '储电系统', itemStyle: { color: '#2196f3' } },
  //         { value: 30, name: '储热系统', itemStyle: { color: '#ff9800' } },
  //         { value: 25, name: '储气系统', itemStyle: { color: '#4caf50' } },
  //       ],
  //       emphasis: {
  //         itemStyle: {
  //           shadowBlur: 10,
  //           shadowOffsetX: 0,
  //           shadowColor: 'rgba(0, 0, 0, 0.5)',
  //         },
  //       },
  //     },
  //   ],
  // };

  const resourceCards = [
    {
      icon: 'i-ant-design:thunderbolt-filled',
      title: '锂电池储能站',
      capacity: '容量: 2.5MWh',
      planned: '120kW',
      actual: '115kW',
      deviation: '-4.2%',
      deviationType: 'negative',
      chartData: [100, 115, 120, 125, 110, 100],
      actualData: [102, 110, 115, 120, 105, 98],
    },
    {
      icon: 'i-ant-design:fire-filled',
      title: '相变储热罐',
      capacity: '容量: 80GJ',
      planned: '80GJ',
      actual: '82GJ',
      deviation: '+2.5%',
      deviationType: 'positive',
      chartData: [75, 78, 80, 82, 78, 75],
      actualData: [76, 79, 82, 84, 80, 76],
    },
    {
      icon: 'i-ant-design:cloud-filled',
      title: '天然气储罐',
      capacity: '容量: 15,000m³',
      planned: '320m³/h',
      actual: '325m³/h',
      deviation: '+1.6%',
      deviationType: 'positive',
      chartData: [310, 315, 320, 315, 310, 305],
      actualData: [312, 318, 325, 320, 312, 308],
    },
    {
      icon: 'i-ant-design:disconnect-outlined',
      title: '可中断负荷',
      capacity: '容量: 150kW',
      planned: '95kW',
      actual: '90kW',
      deviation: '-5.3%',
      deviationType: 'negative',
      chartData: [90, 92, 95, 92, 90, 88],
      actualData: [88, 90, 90, 88, 85, 82],
    },
  ];

  return (
    <div className={styles.dispatchContainer}>
      {/* 主内容区 */}
      <div className={styles.mainContent}>
        {/* 左侧面板 - 调度控制与资源监控 */}
        <div className={classNames('panel', styles.leftPanel)}>
          <div className={styles.panelHeader}>
            <div className={styles.panelTitle}>
              <div className="i-ant-design:sliders-filled"></div>
              <span>调度控制与资源监控</span>
            </div>
            <Button
              className={styles.warnBtn}
              icon={<div className="i-ant-design:bell-filled"></div>}
            >
              偏差告警 (3)
            </Button>
          </div>

          {/* 阶段控制 */}
          <div className={styles.phaseControl}>
            {phases.map((phase) => (
              <div
                key={phase}
                className={classNames(styles.phaseBtn, {
                  [styles.active]: activePhase === phase,
                })}
                onClick={() => setActivePhase(phase)}
              >
                {phase}
              </div>
            ))}
          </div>

          {/* 全局控制 */}
          <div className={styles.globalControls}>
            <Button className={styles.controlBtn}>
              <div className="i-ant-design:import-outlined"></div>
              导入计划
            </Button>
            <Button className={classNames(styles.controlBtn, styles.primary)}>
              <div className="i-ant-design:play-circle-filled"></div>
              启动执行
            </Button>
            <Button className={styles.controlBtn}>
              <div className="i-ant-design:setting-filled"></div>
              参数设置
            </Button>
          </div>

          {/* 资源监控标签 */}
          <div className={styles.resourceTabs}>
            {tabs.map((tab) => (
              <div
                key={tab}
                className={classNames(styles.tab, {
                  [styles.active]: activeTab === tab,
                })}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </div>
            ))}
          </div>

          {/* 资源监控网格 */}
          <div className={styles.resourceGrid} style={{ marginTop: px(30) }}>
            {resourceCards.map((card, index) => (
              <div key={index} className={styles.resourceCard}>
                <div className={styles.resourceHeader}>
                  <div className={styles.resourceIcon}>
                    <div className={`${card.icon}`}></div>
                  </div>
                  <div className={styles.resourceTitle}>
                    <h3>{card.title}</h3>
                    <p>{card.capacity}</p>
                  </div>
                </div>
                <div className={styles.resourceStats}>
                  <div className={styles.stat}>
                    <div className={styles.statValue}>{card.planned}</div>
                    <div className={styles.statLabel}>计划出力</div>
                  </div>
                  <div className={styles.stat}>
                    <div className={styles.statValue}>{card.actual}</div>
                    <div className={styles.statLabel}>实际出力</div>
                  </div>
                  <div className={styles.stat}>
                    <div
                      className={classNames(
                        styles.deviation,
                        styles[card.deviationType],
                      )}
                    >
                      {card.deviation}
                    </div>
                    <div className={styles.statLabel}>偏差率</div>
                  </div>
                </div>
                <div
                  className={styles.chartContainer}
                  style={{ marginTop: px(30), height: px(400) }}
                >
                  <ReactECharts
                    option={getLineChartOption(
                      card.chartData,
                      card.actualData,
                      index === 0
                        ? '#2196f3'
                        : index === 1
                        ? '#ff9800'
                        : index === 2
                        ? '#4caf50'
                        : '#9c27b0',
                      '#4caf50',
                    )}
                    style={{ height: px(350), width: px(300) }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧面板 - 韧性指标与日志分析 */}
        <div className={classNames('panel', styles.rightPanel)}>
          <div className={styles.panelHeader}>
            <div className={styles.panelTitle}>
              <div className="i-ant-design:line-chart-outlined"></div>
              <span>韧性指标与调度分析</span>
            </div>
            <Button
              className={styles.controlBtn}
              icon={<div className="i-ant-design:file-pdf-filled"></div>}
            >
              生成报告
            </Button>
          </div>

          {/* 韧性指标网格 */}
          <div className={styles.metricsGrid}>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>负荷下降率</div>
              <div className={styles.metricValue}>31.6%</div>
              <div
                className={classNames(
                  styles.metricChange,
                  styles.changeNegative,
                )}
              >
                ▼ 10.9%
              </div>
              <div className={styles.metricSubtitle}>调度前: 42.5%</div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>供电恢复率</div>
              <div className={styles.metricValue}>78.5%</div>
              <div
                className={classNames(
                  styles.metricChange,
                  styles.changePositive,
                )}
              >
                ▲ 11.7%
              </div>
              <div className={styles.metricSubtitle}>调度前: 66.8%</div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>恢复时间</div>
              <div className={styles.metricValue}>8h</div>
              <div
                className={classNames(
                  styles.metricChange,
                  styles.changeNegative,
                )}
              >
                ▼ 4h
              </div>
              <div className={styles.metricSubtitle}>调度前: 12h</div>
            </div>
          </div>

          {/* 日志与分析容器 */}
          <div
            className={styles.logAnalysisContainer}
            style={{ maxHeight: px(610) }}
          >
            {/* 调度执行日志 */}
            <div className={styles.logContainer}>
              <div className={styles.logHeader}>
                <div className="i-ant-design:code-filled"></div>
                <span>调度执行日志</span>
              </div>
              <div className={styles.logList}>
                {logEntries.map((entry, index) => (
                  <div key={index} className={styles.logEntry}>
                    <div className={styles.logTime}>{entry.time}</div>
                    <div className={styles.logContent}>
                      <span className={styles.logResource}>
                        {entry.resource}
                      </span>
                      <span
                        className={classNames(
                          styles.logText,
                          entry.type === 'success'
                            ? styles.logSuccess
                            : entry.type === 'warning'
                            ? styles.logWarning
                            : styles.logNormal,
                        )}
                      >
                        {entry.content}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 韧性提升贡献分析
            <div className={styles.analysisContainer}>
              <div className={styles.analysisTitle}>韧性提升贡献分析</div>
              <div className={styles.analysisChart}>
                <ReactECharts
                  option={doughnutOption}
                  style={{ height: '100%', width: '100%' }}
                />
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnergyDispatchPlatform;
