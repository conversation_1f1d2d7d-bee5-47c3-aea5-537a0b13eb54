import { px } from '@/utils/util';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import { option } from './option';
interface Prop {
  data: number[];
}
export default function (prop: Prop) {
  const { data } = prop;
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    myChart.current.setOption(option(data));
  }, [data]);

  return (
    <div
      style={{
        width: '100%',
        height: px(440),
      }}
      ref={container}
    ></div>
  );
}
