import { OrbitControls } from '@react-three/drei';
import { Canvas } from '@react-three/fiber';
import { Suspense, useRef } from 'react';
import * as THREE from 'three';
import { DirectionalLight } from 'three';
import MyModel, { type IndividualModelInfo } from './MyModel';
import { usePanWithSpaceKey } from './hooks/usePanWithSpaceKey';
import styles from './index.sass';

export default function () {
  const mountRef = useRef<HTMLDivElement>(null);
  const lightRef = useRef<DirectionalLight>(null);
  const controlsRef = useRef<any>(null);
  usePanWithSpaceKey(controlsRef);
  // 处理 MyModel 加载完成后的回调
  const handleIndividualModelLoad = (info: IndividualModelInfo[]) => {
    console.log('Received individual model info in App.jsx:', info);
  };

  return (
    <div ref={mountRef} className={styles.box}>
      <Canvas
        style={{ width: '100%', height: '100%' }}
        camera={{
          position: [0, 10, 10], // 相机位置（默认透视相机）
          fov: 75, // 视野角度
          near: 0.1, // 近裁剪面
          far: 2000, // 远裁剪面
        }}
        onCreated={({ gl }) => {
          gl.setClearColor('#233044'); // RGB 绿色
        }}
      >
        <ambientLight intensity={0.5} />
        <directionalLight ref={lightRef} position={[0, 5, 0]} intensity={1} />
        <Suspense fallback={'模型加载中···'}>
          <MyModel onScrewMachineLoad={handleIndividualModelLoad} />
        </Suspense>
        <OrbitControls
          ref={controlsRef}
          enablePan
          enableRotate
          enableZoom
          mouseButtons={{
            LEFT: THREE.MOUSE.ROTATE, // 默认是旋转
            MIDDLE: THREE.MOUSE.DOLLY,
            RIGHT: THREE.MOUSE.PAN,
          }}
          makeDefault // 设置为默认控制器
          target={[0, 0, 0]}
        />
      </Canvas>
    </div>
  );
}
