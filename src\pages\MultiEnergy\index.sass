@import '@/utils/helpers.sass'
.gridLayout
  flex: 1
  display: grid
  grid-template-columns: repeat(4, 1fr)
  grid-template-rows: repeat(6, 1fr)
  gap: px(20)
  min-height: 0
.gridItem:nth-child(1)
  grid-column: 1 / 2
  grid-row: 1 / 4
.gridItem:nth-child(2)
  grid-column: 2 / 4
  grid-row: 1 / 5
  background: rgba(3, 16, 52, 0.8)
  border: 1px solid rgba(5, 126, 255, 0.3)
  border-radius: px(4)
.gridItem:nth-child(3)
  grid-column: 4 / 5
  grid-row: 1 / 4
.gridItem:nth-child(4)
  grid-column: 1 / 2
  grid-row: 4 / 7
.gridItem:nth-child(5)
  grid-column: 2 / 3
  grid-row: 5 / 7
.gridItem:nth-child(6)
  grid-column: 3 / 4
  grid-row: 5 / 7
.gridItem:nth-child(7)
  grid-column: 4 / 5
  grid-row: 4 / 7

.energySummary 
  display: flex
  justify-content: space-between
  color: #b8e6ff
  font-family: inherit
  font-size: px(16)

.energyCol 
  flex: 1
  display: flex
  flex-direction: column
  align-items: flex-start
  &:last-child 
    border-right: none
  

.energyRow 
  display: flex
  align-items: center
  background-image: url('/bg.png')
  background-size: 100% 100%
  background-repeat: no-repeat
  font-size: px(16)
  padding: px(3)
  margin-bottom: px(5)
  &:last-child 
    margin-bottom: 0
  

.energyLabel 
  color: #b8e6ff
  font-size: px(16)

.energyValue 
  color: #4ad1ff
  font-size: px(16)
  font-weight: bold

.energyStatus 
  color: #4ad1ff
  font-size: px(16)

.energyArrowUp 
  color: #00ff6a
  font-size: px(16)

.energyArrowDown 
  color: #ff4d4f
  font-size: px(16)

.energyUnit 
  color: #b8e6ff
  font-size: px(14)

.energyValueCompare 
  color: #b8e6ff
  font-size: px(16)
