import mapboxgl from 'mapbox-gl';
import * as THREE from 'three';
import { Threebox } from 'threebox-plugin';
// 生成随机点数据（仅用于示例）
export const generateRandomPoints = (count) => {
  const features = [];
  const center = [121.4737, 31.2304]; // 上海中心
  const radius = 0.003; // 范围半径（经纬度）

  for (let i = 0; i < count; i++) {
    const lng = center[0] + (Math.random() - 0.5) * 2 * radius;
    const lat = center[1] + (Math.random() - 0.5) * 2 * radius;
    const weight = Math.random(); // 随机权重

    features.push({
      type: 'Feature',
      geometry: { type: 'Point', coordinates: [lng, lat] },
      properties: { weight },
    });
  }

  return {
    type: 'FeatureCollection',
    features,
  };
};
export function setModelHighlight(model) {
  model.traverse((child) => {
    if (child.isMesh) {
      child.material.emissive.set(0x888888);
      child.material.emissiveIntensity = 1;
    }
  });
}

// 重置模型高亮
export function resetModelHighlight(model) {
  model.traverse((child) => {
    if (child.isMesh) {
      child.material.emissive.set(0x000000);
      child.material.emissiveIntensity = 0;
    }
  });
}
// 设置鼠标交互
export function setupHoverEffects(map, tb) {
  let currentHighlightedModel = null;
  let popup = new mapboxgl.Popup({
    className: 'custom-popup',
    closeButton: false,
    closeOnClick: true,
  });
  const mouse = new THREE.Vector2();
  // 如果 pickObject 不可用，尝试手动射线检测
  const raycaster = new THREE.Raycaster();
  map.on('mousemove', (e) => {
    // 清除当前高亮
    if (currentHighlightedModel) {
      resetModelHighlight(currentHighlightedModel);
      currentHighlightedModel = null;
    }
    const intersects = tb.queryRenderedFeatures(e.point);
    let obj = intersects?.[0];
    raycaster.setFromCamera(mouse, tb.camera);

    if (obj && obj.object) {
      // 设置高亮
      setModelHighlight(obj.object);
      currentHighlightedModel = obj.object;
      // 更新鼠标样式
      map.getCanvas().style.cursor = 'pointer';
    } else {
      // 恢复默认样式
      map.getCanvas().style.cursor = '';
    }
  });
  map.on('click', (e) => {
    const intersects = tb.queryRenderedFeatures(e.point);
    let obj = intersects?.[0];
    raycaster.setFromCamera(mouse, tb.camera);
    console.log(e);
    if (obj && obj.object) {
      console.log(obj);
      // 显示弹窗
      let popupContent = `<div>模型名称：${obj.object.name || ''}</div>`;
      popup
        .setLngLat([e.lngLat.lng, e.lngLat.lat])
        .setHTML(popupContent)
        .addTo(map);
    } else {
      // 恢复默认样式
      map.getCanvas().style.cursor = '';
      // 隐藏弹窗
      if (popup.isOpen()) {
        popup.remove();
      }
    }
  });
}
export const addHeatMap = (map) => {
  map.addSource('heatmap-source', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: generateRandomPoints(1000).features, // 生成 1000 个随机点
      //   features: [],
    },
  });

  // 添加热力图层
  map.addLayer({
    id: 'heatmap',
    type: 'heatmap',
    source: 'heatmap-source',
    // maxzoom: 15,
    paint: {
      'heatmap-color': [
        'interpolate',
        ['linear'],
        ['heatmap-density'],
        0,
        'rgba(33,102,172,0)',
        0.2,
        'rgba(65,182,196,0.7)',
        0.4,
        'rgba(127,205,187,0.7)',
        0.6,
        'rgba(199,233,180,0.7)',
        0.8,
        'rgba(230,245,152,0.7)',
        1,
        'rgba(255,255,178,0.7)',
      ],
      'heatmap-radius': ['interpolate', ['linear'], ['zoom'], 0, 2, 15, 50],
      'heatmap-opacity': ['interpolate', ['linear'], ['zoom'], 14, 1, 15, 1],
    },
  });

  // 添加点图层（用于放大后显示）
  // map.addLayer({
  //   id: 'points',
  //   type: 'circle',
  //   source: 'heatmap-source',
  //   minzoom: 15,
  //   paint: {
  //     'circle-radius': 6,
  //     'circle-color': '#007cbf',
  //     'circle-stroke-width': 1,
  //     'circle-stroke-color': '#fff',
  //   },
  // });
};
export const addWarning = (map, animationFrameRef) => {
  // 添加预警点数据源
  map.addSource('alert-points', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: { type: 'Point', coordinates: [121.4739, 31.2324] },
          properties: { id: 1, severity: 'high', description: '主要预警' },
        },
        {
          type: 'Feature',
          geometry: { type: 'Point', coordinates: [121.4837, 31.2204] },
          properties: {
            id: 2,
            severity: 'medium',
            description: '次要预警',
          },
        },
      ],
    },
  });

  // 添加基础圆圈图层
  map.addLayer({
    id: 'alert-circles',
    type: 'circle',
    source: 'alert-points',
    paint: {
      'circle-color': [
        'match',
        ['get', 'severity'],
        'high',
        '#FF4136', // 红色
        'medium',
        '#FF851B', // 橙色
        '#FFDC00', // 默认黄色
      ],
      'circle-radius': 12,
      'circle-opacity': 0.8,
      'circle-stroke-width': 2,
      'circle-stroke-color': '#fff',
    },
  });

  // 添加闪烁圆圈图层
  map.addLayer({
    id: 'alert-pulse',
    type: 'circle',
    source: 'alert-points',
    paint: {
      'circle-color': [
        'match',
        ['get', 'severity'],
        'high',
        '#FF4136',
        'medium',
        '#FF851B',
        '#FFDC00',
      ],
      'circle-radius': 12,
      'circle-opacity': 0,
      'circle-stroke-width': 3,
      'circle-stroke-color': [
        'match',
        ['get', 'severity'],
        'high',
        '#FF4136',
        'medium',
        '#FF851B',
        '#FFDC00',
      ],
      'circle-stroke-opacity': 0.7,
    },
  });

  // 添加点击弹出信息
  map.on('click', 'alert-circles', (e) => {
    const description = e.features[0].properties.description;
    new mapboxgl.Popup()
      .setLngLat(e.lngLat)
      .setHTML(`<h3>预警信息</h3><p>${description}</p>`)
      .addTo(map);
  });
  // 定义闪烁动画
  let pulsePhase = 0;
  function animatePulse() {
    const radius = 12 + 15 * Math.sin(pulsePhase);
    const opacity = 0.7 - 0.5 * Math.abs(Math.sin(pulsePhase));
    map.setPaintProperty(
      'alert-pulse',
      'circle-radius',
      radius > 0 ? radius : 0,
    );
    map.setPaintProperty(
      'alert-pulse',
      'circle-opacity',
      opacity > 0 ? opacity : 0,
    );

    pulsePhase += 0.1;
    animationFrameRef.current = requestAnimationFrame(animatePulse);
  }
  animatePulse();
};
export const addThreeModel = (map, tb) => {
  map.addLayer({
    id: 'custom_layer',
    type: 'custom',
    renderingMode: '3d',
    onAdd: function (map, mbxContext) {
      // eslint-disable-next-line no-param-reassign
      tb = new Threebox(map, mbxContext, {
        defaultLights: true,
        enableSelectingObjects: true, // 启用对象选择
      });
      window.tb = tb;
      // 替代 pickObject
      console.log(mbxContext);
      tb.loadObj({ obj: '/model/园区场景.glb', type: 'gltf' }, (model) => {
        model.setCoords([121.4737, 31.2304, 10]);
        model.rotation.x = Math.PI / 2;
        model.rotation.y = Math.PI / 6;
        tb.add(model);
        // 添加自定义图层
      });
      setupHoverEffects(map, tb);
    },
    render: function () {
      tb.update();
    },
  });
};
