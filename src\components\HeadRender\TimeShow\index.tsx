import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styles from './index.sass';
const weeks = [
  '星期日',
  '星期一',
  '星期二',
  '星期三',
  '星期四',
  '星期五',
  '星期六',
];

export default function () {
  const [year, setYear] = useState('');
  const [time, setTime] = useState('');
  const [week, setWeek] = useState('');
  useEffect(() => {
    const timeinter = setInterval(() => {
      const year = dayjs().format('YYYY-MM-DD');
      const time = dayjs().format('HH:mm:ss');
      setWeek(weeks[dayjs().get('day')]);
      setYear(year);
      setTime(time);
    }, 1000);
    return () => clearInterval(timeinter);
  });
  return (
    <div className={styles.time}>
      <div className={styles.year}>{year}</div>
      <div className={styles.date}>{time}</div>
      <div className={styles.year}>{week}</div>
    </div>
  );
}
