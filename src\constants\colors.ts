// 机组主题色常量
export const UNIT_COLORS = {
  '#1': '#1978E5', // #1 机组 - 蓝色
  '#2F': '#00faed', // #2F 机组 - 青色
  '#2R': '#2ecc71', // #2R 机组 - 绿色 (更深的绿色)
  realTimePrice: '#A571BC', // 实时电价 - 紫色
  dayBeforePrice: '#FACA5D', // 日前电价 - 黄色
  // 功率相关颜色
  actualPower: '#19A3DF', // 实际功率 - 蓝色
  upperLimitPower: '#FFBF00', // 上限功率 - 黄色
  lowerLimitPower: '#32CD32', // 下限功率 - 绿色
  // 基线相关颜色
  telaidianBaseline: '#FF4500', // 特来电基线 - 橙色
  jinanChargingBaseline: '#FF69B4', // 济南充电站基线 - 粉色
  // 负荷相关颜色
  actualLoad: '#19A3DF', // 实际负荷 - 蓝色
  baselineLoad: '#FFBF00', // 基线负荷 - 黄色
  declaredLoad: '#32CD32', // 申报负荷 - 绿色
  // 调节相关颜色
  adjustmentAmount: '#00faed', // 调节量 - 青色
  // 电量相关颜色
  electricConsumption: '#72c6e6', // 用电量 - 浅蓝色
  electricConsumptionDark: '#19334f', // 用电量渐变深色
  // 交易相关颜色
  listingPower: '#5470C6', // 挂牌电量 - 蓝色
  matchingPower: '#FAC858', // 撮合电量 - 黄色
  adjustmentPower: '#73C0DE', // 调整电量 - 浅蓝色
  biddingPower: '#91CC75', // 竞价电量 - 绿色
  listingRevenue: '#EE6666', // 挂牌收益 - 红色
  matchingRevenue: '#3BA272', // 撮合收益 - 绿色
  adjustmentRevenue: '#FF00FF', // 调整收益 - 紫红色
  biddingRevenue: '#73A373', // 竞价收益 - 深绿色
  totalRevenue: '#9A60B4', // 总收益 - 紫色
  // 需求响应相关颜色
  marketType: '#23D4A1', // 市场型 - 绿色
  inviteType: '#477EFF', // 邀约型 - 蓝色
  // 辅助服务相关颜色
  auxiliaryProfit: '#3D83E3', // 辅助服务收益 - 蓝色
  auxiliaryProfitLight: 'rgba(61, 131, 227, 0.8)', // 辅助服务收益渐变浅色
  auxiliaryProfitDark: 'rgba(61, 131, 227, 0.5)', // 辅助服务收益渐变深色
};

// 获取机组颜色函数
export const getUnitColor = (unitType: string): string => {
  if (unitType === '1' || unitType === '#1') return UNIT_COLORS['#1'];
  if (unitType === '2' || unitType === '2F' || unitType === '#2F')
    return UNIT_COLORS['#2F'];
  if (unitType === '2R' || unitType === '#2R') return UNIT_COLORS['#2R'];
  return '#FFFFFF'; // 默认白色
};
