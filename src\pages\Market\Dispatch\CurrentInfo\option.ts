import { px } from '@/utils/util';

export const getOption = (
  value: number,
  sum: number,
  type: 'complete' | 'proportion',
): any => {
  return {
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['60%', '90%'],
        top: '0px',
        left: 0,
        label: {
          show: false,
        },
        hoverAnimation: false,
        tooltip: { show: false },
        data: [
          {
            value: Math.abs(value),
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: type === 'complete' ? '#ace579' : '#89bcd4', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: type === 'complete' ? '#50a12d' : '#0e8bbe', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            label: {
              show: true,
              position: 'center',
              formatter: `${value < 0 ? '{a|-}' : ''}{a|{d}}{a|%}`,

              rich: {
                a: {
                  color: type === 'complete' ? '#50a12d' : '#0e8bbe',
                  lineHeight: 10,
                  fontSize: px(14),
                  fontWeight: 'bolder',
                },
              },
            },
            name: 'Search Engine',
          },
          {
            value: sum - Math.abs(value),
            name: 'Direct',
            itemStyle: { color: '#303d59' },
          },
        ],
      },
    ],
  };
};
