@import '@/utils/helpers.sass'

.heatMapContainer
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  padding: px(10)
  border: 1px solid rgba(255, 255, 255, 0.1)
  margin-top: px(10)

.heatMapHeader
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(15)

.heatMapTitle
  display: flex
  align-items: center
  gap: px(8)

.titleIcon
  font-size: px(18)

.titleText
  color: #fff
  font-size: px(16)
  font-weight: bold

.heatMapLegend
  display: flex
  align-items: center
  gap: px(8)

.legendItem
  font-size: px(12)
  font-weight: normal

.legendSeparator
  color: #fff
  font-size: px(12)

.heatMapChart
  width: 100%
  height: px(240)
