import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { option } from './option';
export default function () {
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    myChart.current.setOption(option());
  }, []);

  return (
    <div className={styles.heatMapContainer}>
      {/* 标题和图例 */}
      <div className={styles.heatMapHeader}>
        <div className={styles.heatMapTitle}>
          <span className={styles.titleIcon}>🏢</span>
          <span className={styles.titleText}>区域韧性力图</span>
        </div>
        <div className={styles.heatMapLegend}>
          <span className={styles.legendItem} style={{ color: '#4CAF50' }}>
            低韧性
          </span>
          <span className={styles.legendSeparator}>-</span>
          <span className={styles.legendItem} style={{ color: '#FFC107' }}>
            中韧性
          </span>
          <span className={styles.legendSeparator}>-</span>
          <span className={styles.legendItem} style={{ color: '#F44336' }}>
            高韧性
          </span>
        </div>
      </div>
      {/* 图表区域 */}
      <div className={styles.heatMapChart} ref={container}></div>
    </div>
  );
}
