import styles from './index.sass';

interface PowerIndicatorProps {
  value: string | number;
  unit: string;
  label: string;
}

export default function PowerIndicator({
  value,
  unit,
  label,
}: PowerIndicatorProps) {
  return (
    <div className={styles.container}>
      <div className={styles.valueContainer}>
        <div className={styles.value}>{value}</div>
      </div>
      <div className={styles.label}>
        {label}({unit})
      </div>
    </div>
  );
}
