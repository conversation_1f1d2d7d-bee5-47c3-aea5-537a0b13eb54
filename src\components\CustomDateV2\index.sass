@import '@/utils/helpers.sass'

.box
    outline: none
    border-radius: px(10)
    width: px(150)
    padding: px(5) px(10)
    background: rgba(0,0,0,0) !important
    :global
        .ant-picker-input >input,.ant-picker-separator,.ant-picker-suffix,
        .ant-picker .ant-picker-input >input::placeholder,
        .ant-select-single .ant-select-selector,
        .ant-select .ant-select-arrow,
        .ant-select-selection-item
            color: white !important
            font-size: px(14)
        .ant-select-selector,.ant-select-single .ant-select-selector
            background: rgba(0,0,0,0)
            border: white solid px(1)
        .ant-select-selector
            padding: px(3) px(10) !important
            height: px(34) !important
        .ant-select-selection-item,.ant-select-selection-placeholder
            font-size: px(14)
            line-height: px(24) !important
        .ant-select-arrow
            margin-top: px(12)
            top: 0
