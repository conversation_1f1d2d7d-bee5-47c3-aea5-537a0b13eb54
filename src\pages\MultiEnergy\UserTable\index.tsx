import CustomTable from '@/components/CustomTable';
import { useAntdTable } from 'ahooks';
import { ColumnsType } from 'antd/es/table';
import styles from './index.sass';

// mock数据函数
function getMockUserTableData(params: { pageSize: number; current: number }) {
  const allList = [
    { name: '用户A', type: '工业', gas: 120, hot: 5000, placement: '东区1号' },
    { name: '用户B', type: '商业', gas: 80, hot: 3200, placement: '西区2号' },
    { name: '用户C', type: '居民', gas: 30, hot: 1200, placement: '南区3号' },
    { name: '用户D', type: '工业', gas: 150, hot: 6000, placement: '北区4号' },
    { name: '用户E', type: '商业', gas: 60, hot: 2500, placement: '东区5号' },
    { name: '用户F', type: '居民', gas: 25, hot: 900, placement: '西区6号' },
    { name: '用户G', type: '工业', gas: 110, hot: 4800, placement: '东区7号' },
    { name: '用户H', type: '商业', gas: 70, hot: 2100, placement: '南区8号' },
    { name: '用户I', type: '居民', gas: 28, hot: 1000, placement: '北区9号' },
  ];
  const { pageSize, current } = params;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  return Promise.resolve({
    total: allList.length,
    list: allList.slice(start, end),
  });
}

export default function () {
  const getTableData = (params: { pageSize: number; current: number }) => {
    return getMockUserTableData(params);
  };

  const { tableProps } = useAntdTable(getTableData, {
    defaultParams: [
      {
        current: 1,
        pageSize: 2,
      },
    ],
    defaultType: 'advance',
  });
  const columns: ColumnsType<any> = [
    {
      title: '用户名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '用户类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
    },
    {
      title: '用气量(m3/h)',
      dataIndex: 'gas',
      key: 'gas',
      align: 'center',
    },
    {
      title: '用热量(kJ)',
      dataIndex: 'hot',
      key: 'hot',
      align: 'center',
    },
    {
      title: '接入位置',
      dataIndex: 'placement',
      key: 'placement',
      align: 'center',
    },
  ];
  return (
    <div className={styles.box}>
      <CustomTable className={styles.table} columns={columns} {...tableProps} />
    </div>
  );
}
