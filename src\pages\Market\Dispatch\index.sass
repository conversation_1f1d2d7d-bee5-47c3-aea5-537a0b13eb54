@import '@/utils/helpers.sass'

.container
  padding: px(20)
  
  
  color: white
  display: flex
  gap: px(10)

.rightSection
  display: flex
  flex-direction: column
  gap: px(10)

.leftPanel
  width: px(460)
  display: flex
  flex-direction: column

.chartContainer
  height: px(540)
  background: rgba(13, 44, 92, 0.6)
  border: px(1) solid rgba(65, 167, 250, 0.3)
  border-radius: px(8)
  margin-top: px(10)
  padding: px(20)

.barChart
  width: 100%
  height: 100%
  display: flex
  flex-direction: column
  gap: px(8)

.barItem
  display: flex
  align-items: center
  gap: px(15)

.barLabel
  color: white
  font-size: px(14)
  width: px(120)
  text-align: right

.barWrapper
  flex: 1
  position: relative
  height: px(25)
  background: rgba(7, 19, 59, 0.5)
  border-radius: px(4)
  display: flex
  align-items: center

.barFill
  height: 100%
  background: linear-gradient(90deg, #4A90E2, #7BB3F0)
  border-radius: px(4)
  position: relative

.barValue
  position: absolute
  right: px(10)
  color: white
  font-size: px(12)
  font-weight: bold

.rightTopPanel
  width: px(1490)
  height: px(150)
  display: flex
  gap: px(20)

.rightBottomPanel
  width: px(1800)
  flex: 1
  display: flex
  flex-direction: column
  gap: px(10)

.modeSection
  flex: 1
  height: px(150)
  background: rgba(13, 44, 92, 0.6)
  border: px(1) solid rgba(65, 167, 250, 0.3)
  border-radius: px(8)
  padding: px(20)
  display: flex
  flex-direction: column
  justify-content: center

.modeTitle
  color: white
  font-size: px(18)
  font-weight: bold
  margin-bottom: px(15)
  text-align: center

.modeButtons
  display: flex
  gap: px(10)

.modeBtn
  flex: 1
  height: px(40)
  background: rgba(7, 19, 59, 0.7)
  border: px(1) solid rgba(65, 167, 250, 0.5)
  color: white
  font-size: px(14)
  border-radius: px(6)

  &:hover
    background: rgba(65, 167, 250, 0.2)
    border-color: rgba(65, 167, 250, 0.8)

  &.active
    background: rgba(24, 93, 158, 1)
    border-color: rgba(24, 93, 158, 1)

.statsSection
  flex: 2
  height: px(150)
  background: rgba(13, 44, 92, 0.6)
  border: px(1) solid rgba(65, 167, 250, 0.3)
  border-radius: px(8)
  padding: px(20)
  display: flex
  gap: px(20)
  align-items: center
  justify-content: center

.statCircle
  display: flex
  flex-direction: column
  align-items: center
  gap: px(10)

.circleProgress
  width: px(120)
  height: px(120)
  border-radius: 50%
  background: conic-gradient(#4A90E2 0deg, #4A90E2 0deg, rgba(13, 44, 92, 0.6) 0deg)
  display: flex
  align-items: center
  justify-content: center
  position: relative

  &.complete
    background: conic-gradient(#4A90E2 0deg, #4A90E2 360deg, rgba(13, 44, 92, 0.6) 360deg)

.circleInner
  width: px(90)
  height: px(90)
  border-radius: 50%
  background: rgba(13, 44, 92, 0.8)
  display: flex
  align-items: center
  justify-content: center

.circleValue
  color: white
  font-size: px(24)
  font-weight: bold

.circleLabel
  color: white
  font-size: px(14)
  text-align: center
  max-width: px(140)



.filterSection

  background: rgba(13, 44, 92, 0.3)
  border-radius: px(8)

.datePicker
  background: rgba(0,0,0,0)
  border: px(1) solid rgba(65, 167, 250, 0.5)
  color: white

.filterBtn
  background: rgba(7, 19, 59, 0.7)
  border: px(1) solid rgba(65, 167, 250, 0.5)
  color: white
  height: px(35)

  &:hover
    background: rgba(65, 167, 250, 0.2)
    border-color: rgba(65, 167, 250, 0.8)

.queryBtn
  background: rgba(24, 93, 158, 1)
  border: px(1) solid rgba(24, 93, 158, 1)
  color: white
  height: px(35)

  &:hover
    background: rgba(24, 93, 158, 0.8)

.ganttContainer
  
  background: rgba(13, 44, 92, 0.6)
  border: px(1) solid rgba(65, 167, 250, 0.3)
  border-radius: px(8)
  padding: px(20)

.ganttHeader
  display: flex
  border-bottom: px(1) solid rgba(65, 167, 250, 0.3)
  padding-bottom: px(10)
  margin-bottom: px(15)

.ganttBody
  display: flex
  flex-direction: column
  gap: px(8)

.ganttRow
  display: flex
  align-items: center
  min-height: px(55)

.ganttRowLabel
  width: px(250)
  color: white
  font-size: px(18)
  padding-right: px(15)
  flex-shrink: 0

.ganttTimeline
  flex: 1
  display: flex
  position: relative
  height: px(30)
  background: rgba(7, 19, 59, 0.3)
  border-radius: px(4)

.timeSlot
  flex: 1
  text-align: center
  color: white
  font-size: px(16)
  padding: px(5)
  border-right: px(1) solid rgba(65, 167, 250, 0.2)

  &:last-child
    border-right: none

.ganttBar
  position: absolute
  height: 100%
  border-radius: px(4)
  display: flex
  align-items: center
  justify-content: center
  color: white
  font-size: px(16)
  font-weight: bold

  &.blueBar
    background: linear-gradient(90deg, #4A90E2, #7BB3F0)

  &.orangeBar
    background: linear-gradient(90deg, #FF8C42, #FFB366)

  &.greenBar
    background: linear-gradient(90deg, #5CB85C, #7BC97B)
