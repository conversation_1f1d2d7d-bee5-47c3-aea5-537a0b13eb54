@import '@/utils/helpers.sass'

@keyframes flash
    0%, 100%
        opacity: 1
    50%
        opacity: 0.5
.head
    color: white
    height: 100%
    width: 100%
    display: flex
    flex-direction: row
    align-items: center
    justify-content: center
    padding: 0 px(20)
    position: relative
.title
    position: relative
    width: 52%
    height: px(85)
    text-align: center
    font-family: pangmen
    font-size: px(38)
    letter-spacing: px(10)
    padding-top: px(5)
    padding-right: px(0)
.menus
    display: flex
    position: absolute
    top: px(48)
    left: px(50)
    font-size: px(16)
    height: px(36)
    line-height: px(18)
.btn
    background-image: url(/btn2.png)
    background-repeat: no-repeat
    background-size: 100% 100%
    cursor: pointer
    display: flex
    align-items: center
    width: px(150)
    justify-content: center
    position: relative
    &:hover
        background-image: url(/btn2act.png)
.dropname
    margin-left: px(18)
    z-index: 33
.btnact
    background-image: url(/btn2act.png)
.rightmenu
    position: absolute
    right: px(50)
    top: px(50)
    display: flex
    font-size: px(16)
    line-height: px(18)
    align-items: center
    font-weight: bold
    .item
        display: flex
        align-items: center
        margin-left: px(16)
        cursor: pointer
    img
        width: px(24)
        height: px(24)
        margin-right: px(8)
