import CustomSelect from '@/components/CustomSelect';
import { px } from '@/utils/util';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { mockElectricLineData } from './mock';
import { option } from './option';

export default function () {
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    myChart.current.setOption(option(mockElectricLineData));

    const handleResize = () => {
      myChart.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      myChart.current?.dispose();
    };
  }, []);

  return (
    <div className={styles.electricLineContainer}>
      <div className={styles.top}>
        <div className={styles.selectContainer}>
          <CustomSelect
            style={{ width: px(150) }}
            options={[
              { value: '年度', label: '年度' },
              { value: '月度', label: '月度' },
            ]}
          />
        </div>
        <div className={styles.datePickerContainer}>
          <DatePicker.RangePicker
            value={[dayjs('2025-02-01'), dayjs('2025-02-26')]}
          />
        </div>
      </div>
      <div
        className={styles.chartContainer}
        ref={container}
        style={{ height: px(400), width: px(520) }}
      ></div>
    </div>
  );
}
