import { UNIT_COLORS } from '@/constants/colors';
import { px } from '@/utils/util';

let data = [];
const dates = [
  '设备1',
  '设备2',
  '设备3',
  '设备4',
  '设备5',
  '设备6',
  '设备7',
  '设备8',
  '设备9',
  '设备10',
  '设备11',
  '设备12',
];
for (let i = 0; i < dates.length; i++) {
  data.push({
    time: dates[i],
    value: (Math.random() * 50 + 100).toFixed(0),
  });
}
export const option = () => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      textStyle: {
        fontSize: px(16),
      },
    },
    grid: {
      left: '1.5%',
      right: '0%',
      bottom: '3%',
      top: '12%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: '#fff',
          fontSize: px(16),
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: '指数',
      nameTextStyle: {
        color: '#fff',
        fontSize: px(16),
      },
      axisLabel: {
        formatter: '{value}',
        textStyle: {
          color: '#fff',
          fontSize: px(16),
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(176,215,255,0.25)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '指数',
        data: data.map((item) => item.value),
        type: 'bar',
        barWidth: px(10),
        itemStyle: {
          borderRadius: [px(10), px(10), 0, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: UNIT_COLORS.electricConsumption,
              },
              {
                offset: 1,
                color: UNIT_COLORS.electricConsumptionDark,
              },
            ],
            global: false,
          },
        },
      },
      {
        type: 'pictorialBar',
        symbol: 'image://symbol.png',
        symbolSize: [px(50), px(50)],
        symbolOffset: [0, px(-20)],
        symbolPosition: 'end',
        data: data.map((item) => item.value),
        z: 3,
        tooltip: {
          show: false,
        },
      },
    ],
  };
};
