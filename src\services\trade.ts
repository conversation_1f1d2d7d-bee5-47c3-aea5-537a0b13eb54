import axios from 'axios';

const ma = 'api';
const mock = true;

export const AGetUnusual = (params: {
  name: string;
  company: string;
  type: string;
  startTime: string;
  endTime: string;
  current: number;
  pageSize: number;
  rank: string;
}) => {
  if (mock) {
    let list = [];
    for (let i = 1; i < 30; i++) {
      list.push({
        time: '2025-01-' + i,
        power: 55,
        price: 66,
        benefit: 236,
        cost: 66,
        value: 22,
      });
    }
    return new Promise((resolve) => {
      resolve({
        list,
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get('/' + ma + '/supply_and_demand/marketChanges/getData', {
          params,
        })
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
