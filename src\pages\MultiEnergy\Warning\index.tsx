import styles from './index.sass';
export default function () {
  return (
    <div className={styles.alertsContainer}>
      <div className={styles.scrollContent}>
        <div className={styles.alertItem}>
          <div className={styles.alertTime}>2025-03-23 11:37:25</div>
          <div className={styles.alertContent}>
            <span className={`${styles.alertLevel} ${styles.level1}`}>
              I 级
            </span>
            <span className={styles.alertText}>
              光伏发电功率异常波动，请立即检查设备状态！
            </span>
          </div>
        </div>
        <div className={styles.alertItem}>
          <div className={styles.alertTime}>2025-03-23 09:37:25</div>
          <div className={styles.alertContent}>
            <span className={`${styles.alertLevel} ${styles.level2}`}>
              II 级
            </span>
            <span className={styles.alertText}>
              储能系统充电功率超过预设阈值，建议及时调整。
            </span>
          </div>
        </div>

        {/* 重复项以实现无缝滚动 */}
        <div className={styles.alertItem}>
          <div className={styles.alertTime}>2025-03-23 11:37:25</div>
          <div className={styles.alertContent}>
            <span className={`${styles.alertLevel} ${styles.level1}`}>
              I 级
            </span>
            <span className={styles.alertText}>
              光伏发电功率异常波动，请立即检查设备状态！
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
