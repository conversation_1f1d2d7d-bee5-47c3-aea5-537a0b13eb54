@import '@/utils/helpers.sass'

.box
  width: 100%
  height: 100%
  display: flex
  gap: px(16)
  padding: px(20)
  background-color: $bg-color

// 左侧全景碳看板
.leftPanel
  flex-grow: 1
  height: 100%
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  padding: px(20)
  display: flex
  flex-direction: column

.mapContainer
  flex: 1
  margin-top: px(20)
  background: rgba(0, 0, 0, 0.3)
  border-radius: px(4)
  position: relative

// 右侧面板
.rightPanel
  width: px(488)
  height: 100%
  display: flex
  flex-direction: column
  gap: px(20)

// 右侧三个区块的通用样式
.topSection,
.middleSection,
.bottomSection
  flex: 1
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  padding: px(20)
  display: flex
  flex-direction: column
  height: px(280) // 固定高度
  overflow: hidden // 防止内容溢出

// 区块头部
.sectionHeader
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(15)

// 时间选择器
.timeSelector
  display: flex
  align-items: center

.timeSelect
  width: px(120)
  font-size: px(14)

// 统计数据容器
.statsContainer
  display: grid
  grid-template-columns: 1fr 1fr
  gap: px(15)
  flex: 1

// 统计项目
.statItem
  background: rgba(255, 255, 255, 0.08)
  border-radius: px(8)
  padding: px(15)
  display: flex
  align-items: center
  gap: px(12)
  border: px(1) solid rgba(255, 255, 255, 0.1)
  transition: all 0.3s ease
  &:hover
    background: rgba(255, 255, 255, 0.12)
    border-color: rgba(255, 255, 255, 0.2)

// 统计图标
.statIcon
  width: px(48)
  height: px(48)
  border-radius: px(8)
  background: linear-gradient(135deg, rgba(65, 167, 250, 0.3), rgba(89, 177, 250, 0.2))
  display: flex
  align-items: center
  justify-content: center
  flex-shrink: 0

.iconPlaceholder
  font-size: px(20)
  color: rgba(255, 255, 255, 0.9)
  font-weight: bold

// 统计内容
.statContent
  flex: 1
  display: flex
  flex-direction: column
  gap: px(2)

.statLabel
  color: rgba(255, 255, 255, 0.7)
  font-size: px(12)
  line-height: 1

.statValue
  color: white
  font-size: px(20)
  font-weight: bold
  line-height: 1.2

.statUnit
  color: rgba(255, 255, 255, 0.6)
  font-size: px(10)
  line-height: 1

// 新闻和市场信息容器
.newsContainer,
.marketContainer
  flex: 1
  margin-top: px(15)
  background: rgba(0, 0, 0, 0.2)
  border-radius: px(4)
  padding: px(15)
  overflow: hidden
  position: relative
  height: px(180) // 固定高度，确保滚动区域稳定

// 滚动内容容器
.scrollContent
  animation: scrollUp 20s linear infinite
  &:hover
    animation-play-state: paused

// 滚动动画 - 向上滚动一个完整的循环
@keyframes scrollUp
  0%
    transform: translateY(0)
  100%
    transform: translateY(-50%)

// 新闻项目样式
.newsItem,
.marketItem
  margin-bottom: px(15)
  opacity: 1
  &:last-child
    margin-bottom: px(15) // 保持底部间距，确保滚动连续性

// 时间样式
.newsTime,
.marketTime
  color: rgba(65, 167, 250, 0.9)
  font-size: px(12)
  margin-bottom: px(8)
  display: flex
  align-items: center
  &::after
    content: "▶▶▶"
    margin-left: px(10)
    color: rgba(65, 167, 250, 0.6)
    font-size: px(10)

// 内容容器
.newsContent,
.marketContent
  display: flex
  align-items: flex-start
  gap: px(10)
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))
  padding: px(10) px(12)
  border-radius: px(4)
  border-left: px(3) solid rgba(65, 167, 250, 0.5)
  cursor: pointer
  transition: all 0.3s ease
  &:hover
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))
    border-left-color: rgba(65, 167, 250, 0.8)
    transform: translateX(px(3))

// 等级标签
.newsLevel,
.marketLevel
  flex-shrink: 0
  padding: px(2) px(8)
  border-radius: px(3)
  font-size: px(10)
  font-weight: bold
  color: white
  display: flex
  align-items: center
  min-width: px(35)
  justify-content: center

// 等级颜色
.level1
  background: linear-gradient(135deg, #ff4757, #ff3742)
  box-shadow: 0 0 px(8) rgba(255, 71, 87, 0.4)

.level2
  background: linear-gradient(135deg, #ffa502, #ff9500)
  box-shadow: 0 0 px(8) rgba(255, 165, 2, 0.4)

// 文本内容
.newsText,
.marketText
  color: rgba(255, 255, 255, 0.9)
  font-size: px(12)
  line-height: 1.4
  flex: 1
.map
  width: 100%
  height: 100%
  border: '1px solid #ddd'
  overflow: 'hidden'
