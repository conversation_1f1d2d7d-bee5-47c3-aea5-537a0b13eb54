import { px } from '@/utils/util';
import { ConfigProvider, Tabs, TabsProps } from 'antd';
import Dispatch from './Dispatch';
import Forecasting from './Forecasting';
import styles from './index.sass';
import ResourceMange from './ResourceMange';
import Settlement from './Settlement';
import Trade from './Trade';

const items: TabsProps['items'] = [
  {
    key: '1',
    label: '资源管理',
    children: <ResourceMange></ResourceMange>,
  },
  {
    key: '2',
    label: '预测分析',
    children: <Forecasting></Forecasting>,
  },
  {
    key: '3',
    label: '智能调度',
    children: <Dispatch></Dispatch>,
  },
  {
    key: '4',
    label: '市场交易',
    children: <Trade></Trade>,
  },
  {
    key: '5',
    label: '结算分配',
    children: <Settlement></Settlement>,
  },
];

export default function () {
  return (
    <ConfigProvider
      theme={{
        token: {
          /* 这里是你的全局 token */
          fontSize: px(18),
          controlHeight: px(35),
        },
        components: {
          Tabs: {
            cardBg: 'rgba(0,0,0,0)',
            colorText: '#fff',
            itemSelectedColor: '#fff',
            colorBgContainer: '#4987D4',
            colorBorder: '#4987D4',
            colorBorderSecondary: '#4987D4',
            marginXXS: px(20),
            horizontalMargin: `0 0  ${px(16)}px 0`,
            cardHeight: px(40),
            cardPadding: `${px(8)}px  ${px(16)}px`,
          },
        },
      }}
    >
      <Tabs
        className={styles.box}
        type="card"
        defaultActiveKey="1"
        items={items}
      />
    </ConfigProvider>
  );
}
