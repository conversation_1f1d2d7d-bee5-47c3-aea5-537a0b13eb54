@import '@/utils/helpers.sass'

$bdcolor: #4987D4
$bdwidth: px(4)
.box
  display: flex
  flex-direction: column
  font-size: px(18)
  color: white
  background: linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,
  background-size: px(20) px(20)
  background-repeat: no-repeat
  background-color: rgba(13, 44, 91, 0.2)
  box-shadow: inset 0 0 px(4) 2px #2F60A1
  height: px(150)
  width: fit-content
  padding: px(20) px(50)
  align-items: center

.title
  font-size: px(20)
  margin-bottom: px(20)
  font-weight: bolder

.items
  display: flex
  flex-direction: row

  &>div
    width: px(180)
    padding: px(15) px(25)
    text-align: center

.cost
  background-image: url('@/assets/bg/efficientcontrol/bg2.png')
  background-size: 100% 100%
  color: gray
  cursor: pointer

.market
  background-image: url('@/assets/bg/efficientcontrol/bg3.png')
  background-size: 100% 100%
  margin-right: px(40)
  color: gray
  cursor: pointer
.selected
  color: white
  font-weight: bold
.market:hover,
.cost:hover
  color: white
  font-weight: bold
