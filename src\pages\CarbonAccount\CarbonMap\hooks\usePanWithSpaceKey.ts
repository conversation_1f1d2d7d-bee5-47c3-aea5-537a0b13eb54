// hooks/usePanWithSpaceKey.ts
import { useEffect } from 'react';
import * as THREE from 'three';

export function usePanWithSpaceKey(controls: React.RefObject<any>) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && controls.current) {
        controls.current.mouseButtons.LEFT = THREE.MOUSE.PAN;
        document.body.style.cursor = 'grab';
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space' && controls.current) {
        controls.current.mouseButtons.LEFT = THREE.MOUSE.ROTATE;
        document.body.style.cursor = 'default';
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [controls]);
}
