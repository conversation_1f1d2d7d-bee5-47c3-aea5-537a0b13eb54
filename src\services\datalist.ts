import axios from 'axios';

const ma = 'api';
const mock = true;

export const AGetUnusual = (params: {
  name: string;
  company: string;
  type: string;
  startTime: string;
  endTime: string;
  current: number;
  pageSize: number;
  rank: string;
}) => {
  if (mock) {
    let list = [];
    for (let i = 1; i < 30; i++) {
      list.push({
        time: '2025-01-' + i,
        name: '测试名称1',
        power: 236,
        state: '已完成场景生成',
      });
    }
    return new Promise((resolve) => {
      resolve({
        list,
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get('/' + ma + '/supply_and_demand/marketChanges/getData', {
          params,
        })
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
