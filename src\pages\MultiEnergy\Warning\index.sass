@import '@/utils/helpers.sass'
// 预警信息容器
.alertsContainer
  flex: 1
  margin-top: px(10)
  background: rgba(0, 0, 0, 0.2)
  border-radius: px(4)
  padding: px(15)
  overflow: hidden
  position: relative

// 滚动内容容器
.scrollContent
  animation: scrollUp 20s linear infinite
  &:hover
    animation-play-state: paused

// 滚动动画
@keyframes scrollUp
  0%
    transform: translateY(0)
  100%
    transform: translateY(-50%)

// 预警项目样式
.alertItem
  margin-bottom: px(15)
  opacity: 1
  &:last-child
    margin-bottom: px(15)

// 时间样式
.alertTime
  color: rgba(65, 167, 250, 0.9)
  font-size: px(12)
  margin-bottom: px(8)
  display: flex
  align-items: center
  &::after
    content: "▶▶▶"
    margin-left: px(10)
    color: rgba(65, 167, 250, 0.6)
    font-size: px(10)

// 预警内容
.alertContent
  display: flex
  align-items: flex-start
  gap: px(10)
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))
  padding: px(10) px(12)
  border-radius: px(4)
  border-left: px(3) solid rgba(65, 167, 250, 0.5)
  cursor: pointer
  transition: all 0.3s ease
  &:hover
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))
    border-left-color: rgba(65, 167, 250, 0.8)
    transform: translateX(px(3))

// 预警等级
.alertLevel
  flex-shrink: 0
  padding: px(2) px(8)
  border-radius: px(3)
  font-size: px(10)
  font-weight: bold
  color: white
  display: flex
  align-items: center
  min-width: px(35)
  justify-content: center

// 等级颜色
.level1
  background: linear-gradient(135deg, #ff4757, #ff3742)
  box-shadow: 0 0 px(8) rgba(255, 71, 87, 0.4)

.level2
  background: linear-gradient(135deg, #ffa502, #ff9500)
  box-shadow: 0 0 px(8) rgba(255, 165, 2, 0.4)

// 预警文本
.alertText
  color: rgba(255, 255, 255, 0.9)
  font-size: px(12)
  line-height: 1.4
  flex: 1