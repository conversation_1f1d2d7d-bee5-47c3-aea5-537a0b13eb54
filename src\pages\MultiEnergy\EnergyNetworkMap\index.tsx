import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import React, { useCallback, useState } from 'react';
import {
  ControlPanel,
  EnergyNetworkMapProps,
  FacilityData,
  FacilityInfoPanel,
  FaultPopup,
  HoverPopupState,
  LoadingOverlay,
  MapContainer,
  MapDataManager,
  MapLayerManager,
  MapLegend,
  PipelineData,
  RealTimeData,
} from './components';
import styles from './index.sass';

// 设置 Mapbox 访问令牌（使用公共令牌或配置环境变量）
mapboxgl.accessToken =
  'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

/**
 * 能源网络地图主组件
 * 重构后的组件使用子组件来管理不同的功能模块
 */
const EnergyNetworkMap: React.FC<EnergyNetworkMapProps> = ({ className }) => {
  // 状态管理
  const [map, setMap] = useState<mapboxgl.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [showHeatNetwork, setShowHeatNetwork] = useState(true);
  const [showGasNetwork, setShowGasNetwork] = useState(true);
  const [showFacilities, setShowFacilities] = useState(true);
  const [selectedFacility, setSelectedFacility] = useState<FacilityData | null>(
    null,
  );
  const [hoverPopup, setHoverPopup] = useState<HoverPopupState>({
    visible: false,
    x: 0,
    y: 0,
    faultInfo: null,
  });
  const [realTimeData, setRealTimeData] = useState<RealTimeData>({
    pipelines: [],
    facilities: [],
  });

  // 事件处理函数
  const handleMapLoad = useCallback((mapInstance: mapboxgl.Map) => {
    setMap(mapInstance);
    setIsLoaded(true);
  }, []);

  const handleDataUpdate = useCallback((data: RealTimeData) => {
    setRealTimeData(data);
  }, []);

  const handleToggleLayer = useCallback(
    (layerType: 'heat' | 'gas' | 'facilities') => {
      switch (layerType) {
        case 'heat':
          setShowHeatNetwork(!showHeatNetwork);
          break;
        case 'gas':
          setShowGasNetwork(!showGasNetwork);
          break;
        case 'facilities':
          setShowFacilities(!showFacilities);
          break;
      }
    },
    [showHeatNetwork, showGasNetwork, showFacilities],
  );

  // 工具函数
  const getStatusText = useCallback((status: string) => {
    switch (status) {
      case 'normal':
        return '正常';
      case 'warning':
        return '警告';
      case 'error':
        return '故障';
      case 'running':
        return '运行中';
      case 'stopped':
        return '停止';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  }, []);

  const getFacilityTypeText = useCallback((type: string) => {
    switch (type) {
      case 'power_plant':
        return '热电联产电厂';
      case 'substation':
        return '变电站';
      case 'storage':
        return '储能站';
      case 'heat_user':
        return '热用户';
      case 'gas_user':
        return '气用户';
      case 'industrial_user':
        return '工业用户';
      default:
        return '未知设施';
    }
  }, []);

  const handlePipelineClick = useCallback(
    (pipeline: PipelineData, e: mapboxgl.MapMouseEvent) => {
      // 显示管道信息弹窗
      new mapboxgl.Popup()
        .setLngLat(e.lngLat)
        .setHTML(
          `
        <div style="color: #000; padding: 10px;">
          <h4>${pipeline.type === 'heat' ? '热网管道' : '气网管道'}</h4>
          <p>流量: ${pipeline.flow.toFixed(1)} ${
            pipeline.type === 'heat' ? 'GJ/h' : 'm³/h'
          }</p>
          <p>状态: ${getStatusText(pipeline.status)}</p>
        </div>
      `,
        )
        .addTo(map!);
    },
    [map, getStatusText],
  );

  const handleFacilityClick = useCallback((facility: FacilityData) => {
    setSelectedFacility(facility);
  }, []);

  const handlePipelineHover = useCallback(
    (pipeline: PipelineData | null, e?: mapboxgl.MapMouseEvent) => {
      if (
        pipeline &&
        pipeline.faultInfo &&
        e &&
        (pipeline.status === 'error' || pipeline.status === 'warning')
      ) {
        const canvas = map!.getCanvas();
        const rect = canvas.getBoundingClientRect();
        setHoverPopup({
          visible: true,
          x: e.point.x + rect.left,
          y: e.point.y + rect.top,
          faultInfo: pipeline.faultInfo,
        });
      } else {
        setHoverPopup({
          visible: false,
          x: 0,
          y: 0,
          faultInfo: null,
        });
      }
    },
    [map],
  );

  const handleFacilityHover = useCallback(
    (facility: FacilityData | null, e?: mapboxgl.MapMouseEvent) => {
      if (
        facility &&
        facility.faultInfo &&
        e &&
        (facility.status === 'error' || facility.status === 'warning')
      ) {
        const canvas = map!.getCanvas();
        const rect = canvas.getBoundingClientRect();
        setHoverPopup({
          visible: true,
          x: e.point.x + rect.left,
          y: e.point.y + rect.top,
          faultInfo: facility.faultInfo,
        });
      } else {
        setHoverPopup({
          visible: false,
          x: 0,
          y: 0,
          faultInfo: null,
        });
      }
    },
    [map],
  );

  return (
    <div className={`${styles.mapContainer} ${className || ''}`}>
      {/* 地图容器 */}
      <MapContainer
        onMapLoad={handleMapLoad}
        onMapClick={() => {}}
        onMapMouseEnter={() => {}}
        onMapMouseLeave={() => {}}
        className={styles.map}
      />

      {/* 数据管理器 */}
      <MapDataManager onDataUpdate={handleDataUpdate} />

      {/* 图层管理器 */}
      <MapLayerManager
        map={map}
        data={realTimeData}
        showHeatNetwork={showHeatNetwork}
        showGasNetwork={showGasNetwork}
        showFacilities={showFacilities}
        onPipelineClick={handlePipelineClick}
        onFacilityClick={handleFacilityClick}
        onPipelineHover={handlePipelineHover}
        onFacilityHover={handleFacilityHover}
      />

      {/* 控制面板 */}
      {isLoaded && (
        <ControlPanel
          showHeatNetwork={showHeatNetwork}
          showGasNetwork={showGasNetwork}
          showFacilities={showFacilities}
          onToggleLayer={handleToggleLayer}
        />
      )}

      {/* 图例 */}
      {isLoaded && <MapLegend />}

      {/* 设施信息面板 */}
      {selectedFacility && (
        <FacilityInfoPanel
          facility={selectedFacility}
          onClose={() => setSelectedFacility(null)}
          getFacilityTypeText={getFacilityTypeText}
          getStatusText={getStatusText}
        />
      )}

      {/* 故障悬浮弹窗 */}
      <FaultPopup
        visible={hoverPopup.visible}
        x={hoverPopup.x}
        y={hoverPopup.y}
        faultInfo={hoverPopup.faultInfo}
      />

      {/* 加载覆盖层 */}
      <LoadingOverlay isLoading={!isLoaded} />
    </div>
  );
};

export default EnergyNetworkMap;
