@import '@/utils/helpers.sass'

@font-face
    font-family: pangmen
    src: url('/font/PangMenZhengDaoBiaoTiTiMianFeiBan-2.ttf')
@font-face
    font-family: youshe
    src: url('/font/YouSheBiaoTiHei-2.ttf') format('truetype')
@font-face
    font-family: shuhei
    src: url('/font/shuhei/AlimamaShuHeiTi-Bold.ttf')
@font-face
    font-family: zhanku
    src: url('/font/TsangerYuYangT_W04_W04.ttf')
// 定义背景色变量
.main
    display: flex
    align-items: center
    justify-content: center
    height: 100vh
    width: 100vw
    padding: 0
    overflow: hidden
.outClass
    :global
        .ant-pro-layout .ant-pro-layout-bg-list
            background: $bg-color
        .ant-pro-layout-container
            background-image: url(/bg.svg)
            background-size: 100% 100%
            background-repeat: no-repeat
        .ant-pro-layout .ant-pro-layout-content
            padding-block: px(20)
            padding-inline: px(40)
        .ant-pro-layout .ant-layout-header.ant-pro-layout-header-fixed-header
            // top: px(0)
            background-color: black
        .ant-pro-layout .ant-layout-header.ant-pro-layout-header
            height: px(80)
            line-height: px(80)
            background: rgba(0,0 ,0 ,0 )
            backdrop-filter: blur(0)
