import CustomSelectV2 from '@/components/CustomSelectV2';
import CustomSubTitle from '@/components/CustomSubTitle';
import CustomTitle from '@/components/CustomTitle';
import PowerIndicator from '@/components/PowerIndicator';
import { px } from '@/utils/util';
import ActivePower from './ActivePower';
import ChargeDischarge from './ChargeDischarge';
import styles from './index.sass';

const Index = () => {
  return (
    <div className={styles.container}>
      <div className={styles.leftSection}>
        <CustomTitle>实时数据统计</CustomTitle>
        <div className={styles.indicateContainer}>
          <CustomSubTitle>实时功率指标</CustomSubTitle>
          <div className={styles.indicate}>
            <PowerIndicator value={180} unit="kW" label="有功功率" />
            <PowerIndicator value={65} unit="kVar" label="无功功率" />
            <PowerIndicator value={80} unit="kW" label="光伏发电功率" />
          </div>
        </div>
        <div className={styles.chartsContainer}>
          <CustomSubTitle>功率变化折线图</CustomSubTitle>
          <CustomSelectV2
            style={{ width: px(100), height: px(30), marginTop: px(10) }}
            options={[
              { label: '日', value: 'day' },
              { label: '月', value: 'month' },
              { label: '年', value: 'year' },
            ]}
            onChange={(value) => {
              console.log(value);
            }}
          />
          <div className={`${styles.chartRow} ${styles.activePower}`}>
            <div className={styles.labelContainer}>
              <div className={styles.labelBar}></div>
              <div className={styles.label}>有功功率</div>
            </div>
            <div className={styles.chartContent}>
              <ActivePower />
            </div>
          </div>
          <div className={`${styles.chartRow} ${styles.reactivePower}`}>
            <div className={styles.labelContainer}>
              <div className={styles.labelBar}></div>
              <div className={styles.label}>无功功率</div>
            </div>
            <div className={styles.chartContent}>
              <ActivePower />
            </div>
          </div>
          <div className={`${styles.chartRow} ${styles.solarPower}`}>
            <div className={styles.labelContainer}>
              <div className={styles.labelBar}></div>
              <div className={styles.label}>光伏发电功率</div>
            </div>
            <div className={styles.chartContent}>
              <ActivePower />
            </div>
          </div>
        </div>
      </div>

      <div className={styles.middleSection}>
        <div className={styles.mapSection}>{/* 地图部分 */}</div>
        <div className={styles.infoSection}>
          <div className={styles.indicators}>
            <CustomTitle>区域实时统计指标</CustomTitle>
            <div className={styles.statsContainer}>
              <div className={styles.statItem}>
                <div className={styles.statIcon}>
                  <div className={styles.iconPlaceholder}>⚡</div>
                </div>
                <div className={styles.statContent}>
                  <div className={styles.statLabel}>总有功功率</div>
                  <div className={styles.statValue}>2,000</div>
                  <div className={styles.statUnit}>kW</div>
                </div>
              </div>

              <div className={styles.statItem}>
                <div className={styles.statIcon}>
                  <div className={styles.iconPlaceholder}>🔄</div>
                </div>
                <div className={styles.statContent}>
                  <div className={styles.statLabel}>总无功功率</div>
                  <div className={styles.statValue}>700</div>
                  <div className={styles.statUnit}>kVar</div>
                </div>
              </div>

              <div className={styles.statItem}>
                <div className={styles.statIcon}>
                  <div className={styles.iconPlaceholder}>☀️</div>
                </div>
                <div className={styles.statContent}>
                  <div className={styles.statLabel}>光伏发电功率</div>
                  <div className={styles.statValue}>1,288</div>
                  <div className={styles.statUnit}>kW</div>
                </div>
              </div>

              <div className={styles.statItem}>
                <div className={styles.statIcon}>
                  <div className={styles.iconPlaceholder}>⚖️</div>
                </div>
                <div className={styles.statContent}>
                  <div className={styles.statLabel}>储能系统净功率</div>
                  <div className={styles.statValue}>30</div>
                  <div className={styles.statUnit}>kW</div>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.alerts}>
            <CustomTitle>历史预警信息</CustomTitle>
            <div className={styles.alertsContainer}>
              <div className={styles.scrollContent}>
                <div className={styles.alertItem}>
                  <div className={styles.alertTime}>2025-03-23 11:37:25</div>
                  <div className={styles.alertContent}>
                    <span className={`${styles.alertLevel} ${styles.level1}`}>
                      I 级
                    </span>
                    <span className={styles.alertText}>
                      光伏发电功率异常波动，请立即检查设备状态！
                    </span>
                  </div>
                </div>

                <div className={styles.alertItem}>
                  <div className={styles.alertTime}>2025-03-23 09:37:25</div>
                  <div className={styles.alertContent}>
                    <span className={`${styles.alertLevel} ${styles.level2}`}>
                      II 级
                    </span>
                    <span className={styles.alertText}>
                      储能系统充电功率超过预设阈值，建议及时调整。
                    </span>
                  </div>
                </div>

                <div className={styles.alertItem}>
                  <div className={styles.alertTime}>2025-03-22 12:17:25</div>
                  <div className={styles.alertContent}>
                    <span className={`${styles.alertLevel} ${styles.level1}`}>
                      I 级
                    </span>
                    <span className={styles.alertText}>
                      系统检测到无功功率突增，可能存在设备故障！
                    </span>
                  </div>
                </div>

                {/* 重复项以实现无缝滚动 */}
                <div className={styles.alertItem}>
                  <div className={styles.alertTime}>2025-03-23 11:37:25</div>
                  <div className={styles.alertContent}>
                    <span className={`${styles.alertLevel} ${styles.level1}`}>
                      I 级
                    </span>
                    <span className={styles.alertText}>
                      光伏发电功率异常波动，请立即检查设备状态！
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.rightSection}>
        <CustomTitle>储能实时监测数据</CustomTitle>
        <div className={styles.content}>
          <CustomSubTitle>运行实态</CustomSubTitle>
          <div className={styles.statusOverview}>
            <div className={styles.statusItem}>
              <div className={styles.statusLabel}>设备总数</div>
              <div className={styles.statusValue}>20</div>
            </div>
            <div className={styles.statusItem}>
              <div className={styles.statusLabel}>正常数量</div>
              <div className={styles.statusValue}>19</div>
            </div>
          </div>

          <div className={styles.deviceList}>
            {[1, 2, 3].map((num) => (
              <div key={num} className={styles.deviceItem}>
                <div className={styles.deviceIcon}>
                  <img src="/charger.png" alt="充电桩" />
                </div>
                <div className={styles.deviceInfo}>
                  <div className={styles.deviceName}>{num}#PCS</div>
                  <div className={styles.deviceStatus}>
                    <span className={styles.statusTag}>运行</span>
                    <span className={`${styles.statusTag} ${styles.charging}`}>
                      充电
                    </span>
                  </div>
                </div>
                <div className={styles.deviceMetrics}>
                  <span className={styles.metric}>50%</span>
                  <span className={styles.metric}>38°C</span>
                </div>
              </div>
            ))}
          </div>

          <CustomSubTitle>充放电量柱状图</CustomSubTitle>
          <CustomSelectV2
            style={{
              width: px(100),
              height: px(30),
              marginTop: px(5),
              marginBottom: px(5),
            }}
            options={[
              { label: '日', value: 'day' },
              { label: '月', value: 'month' },
              { label: '年', value: 'year' },
            ]}
            onChange={(value) => {
              console.log(value);
            }}
          />
          <div className={styles.chartWrapper}>
            <ChargeDischarge />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
