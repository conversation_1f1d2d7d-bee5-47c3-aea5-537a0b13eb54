import { px } from '@/utils/util';
import { useAntdTable } from 'ahooks';
import { ConfigProvider, Form, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import styles from './index.sass';

// mock数据函数
function getMockEquipmentTableData(params: {
  pageSize: number;
  current: number;
}) {
  const allList = [
    {
      name: 'EQ-001',
      power: '慈溪储能1号',
      area: '氢储能',
      location: '慈溪市观海卫镇',
    },
    {
      name: 'EQ-002',
      power: '慈溪储能2号',
      area: '电池储能',
      location: '慈溪市龙山镇',
    },
    {
      name: 'EQ-003',
      power: '慈溪储能3号',
      area: '氢储能',
      location: '慈溪市坎墩街道',
    },
    {
      name: 'EQ-004',
      power: '慈溪储能4号',
      area: '热储能',
      location: '慈溪市古塘街道',
    },
    {
      name: 'EQ-005',
      power: '慈溪储能5号',
      area: '氢储能',
      location: '慈溪市白沙路街道',
    },
    {
      name: 'EQ-006',
      power: '慈溪储能6号',
      area: '电池储能',
      location: '慈溪市浒山街道',
    },
    {
      name: 'EQ-007',
      power: '慈溪储能7号',
      area: '热储能',
      location: '慈溪市宗汉街道',
    },
  ];
  const { pageSize, current } = params;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  return Promise.resolve({
    total: allList.length,
    list: allList.slice(start, end),
  });
}

export default function () {
  const [form] = Form.useForm();
  const getTableData = (params: { pageSize: number; current: number }) => {
    return getMockEquipmentTableData(params);
  };

  const { tableProps } = useAntdTable(getTableData, {
    defaultParams: [
      {
        current: 1,
        pageSize: 5,
      },
    ],
    form,
    defaultType: 'advance',
  });
  const columns: ColumnsType<any> = [
    {
      title: '设备编号',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '慈溪本地名称',
      dataIndex: 'power',
      key: 'power',
      align: 'center',
    },
    {
      title: '储能类型',
      dataIndex: 'area',
      key: 'area',
      align: 'center',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
      align: 'center',
    },
  ];
  return (
    <ConfigProvider
      theme={{
        token: {},
        components: {
          Table: {
            padding: px(5),
            colorBgContainer: '#0E4280',
            headerBg: '#0E4280',
            borderColor: '#1C72E2',
            colorText: '#fff',
            colorTextHeading: '#fff',
            lineType: 'dashed',
            cellPaddingBlock: px(12),
          },
          Tag: {
            colorTextLightSolid: '#000',
          },
          Form: {
            labelColor: '#fff',
          },
          Pagination: {
            colorText: '#fff',
            itemBg: 'rgba(0,0,0,0)',
            colorBgContainer: 'rgba(0,0,0,0)',
            colorPrimary: '#fff',
            colorTextDisabled: '#999',
          },
        },
      }}
    >
      <div className={styles.box}>
        <Table
          className={styles.table}
          columns={columns}
          {...tableProps}
        ></Table>
      </div>
    </ConfigProvider>
  );
}
