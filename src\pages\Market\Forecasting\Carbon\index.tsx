import CustomSelectV2 from '@/components/CustomSelectV2';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { option } from './option';
interface Prop {
  data?: number[];
}
export default function (prop: Prop) {
  const {} = prop;
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    myChart.current.setOption(option());
  }, []);

  return (
    <>
      <CustomSelectV2
        className={styles.select}
        defaultValue={'多能耦合预测'}
        options={[
          {
            label: '多能耦合预测',
            value: '多能耦合预测',
          },
        ]}
      ></CustomSelectV2>
      <div className={styles.chart} ref={container}></div>
    </>
  );
}
