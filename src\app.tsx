/* eslint-disable react-hooks/rules-of-hooks */
// 运行时配置

import { RequestConfig, RunTimeLayoutConfig, history } from '@umijs/max';
import { message, notification } from 'antd';
import axios, { AxiosResponse } from 'axios';
import React from 'react';
import styles from './app.sass';
import HeadRender from './components/HeadRender';
import { px } from './utils/util';

const token = localStorage.getItem('token');
// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState() {
  if (token) {
    axios.interceptors.request.use((config) => {
      //检查是否需要跳过拦截器
      if (config.headers['X-Skip-Interceptor'] === 'true') {
        delete config.headers['X-Skip-Interceptor'];
        return config; //直接返回，不添加unitType
      }
      config.headers.token = token;
      return config;
    });
    axios.interceptors.response.use(
      (response: AxiosResponse) => {
        if (response?.data?.success || response?.data instanceof Blob) {
          return response;
        } else {
          return Promise.reject(response);
        }
      },
      (error) => {
        return Promise.reject(error);
      },
    );
  }

  return {};
}
// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}
// 与后端约定的响应数据格式
interface ResponseStructure {
  success: boolean;
  data: any;
  errorCode?: number;
  errorMessage?: string;
  showType?: ErrorShowType;
}

export const layout: RunTimeLayoutConfig = () => {
  return {
    logo: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
    layout: 'top',
    fixedHeader: false,
    headerRender: HeadRender,
    contentStyle: {
      height: (window as any).pageHeight - px(70),
    },
  };
};

export function rootContainer(container: React.ReactNode) {
  return (
    <div className={styles.main}>
      <div
        style={{
          width: (window as any).pageWidth,
          height: (window as any).pageHeight,
        }}
        className={styles.outClass}
      >
        {container}
      </div>
    </div>
  );
}

export const request: RequestConfig = {
  // timeout: 30000,

  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res: ResponseStructure) => {
      const { success, data, errorCode, errorMessage, showType } = res;
      if (!success) {
        const error: any = new Error(errorMessage);
        error.name = 'BizError';
        error.info = { errorCode, errorMessage, showType, data };
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorInfo.showType) {
            case ErrorShowType.SILENT:
              // do nothing
              break;
            case ErrorShowType.WARN_MESSAGE:
              message.warning(errorMessage);
              break;
            case ErrorShowType.ERROR_MESSAGE:
              message.error(errorMessage);
              break;
            case ErrorShowType.NOTIFICATION:
              notification.open({
                description: errorMessage,
                message: errorCode,
              });
              break;
            case ErrorShowType.REDIRECT:
              // TODO: redirect
              break;
            default:
              message.error(errorMessage);
          }
        }
      } else if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        if (error.response.status === 401) {
          // removeToken();
          message.error(error?.response?.data?.errorMessage + '， 请重新登录');
          history.push('/alogin');
        }
        message.error(error?.response?.data?.errorMessage || '请求失败');
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('None response! Please retry.');
      } else {
        // 发送请求时出了点问题
        message.error('Request error, please retry.');
      }
    },
  },
  requestInterceptors: [
    (url: any, options: any) => {
      const unitTypeFromStorage = localStorage.getItem('unitType');
      const currentUnitType =
        !unitTypeFromStorage || unitTypeFromStorage.trim() === ''
          ? '#2R'
          : unitTypeFromStorage;

      let finalUrl = url;
      // 处理 unitType 参数，所有请求类型都添加到 URL 上
      const urlObj = new URL(finalUrl, window.location.origin);
      urlObj.searchParams.delete('unitType');
      urlObj.searchParams.append('unitType', currentUnitType);
      finalUrl = urlObj.pathname + urlObj.search;

      // 处理 token
      if (token) {
        const headers = {
          ...options.headers,
          Authorization: `Bearer ${token}`,
          token,
        };
        options.headers = headers;
      }

      return { url: finalUrl, options };
    },
  ],
  responseInterceptors: [
    (response: any) => {
      const { data } = response;
      if (!data || !data.hasOwnProperty('success')) {
        message.error('请求失败！');
      }
      return response;
    },
  ],
};
