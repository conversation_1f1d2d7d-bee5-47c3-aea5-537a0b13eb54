@import '@/utils/helpers.sass'
.box
    display: flex
    width: 100%
    height: 100%
.left
    width: calc( 100% - px(520) )
    margin-right: px(20)
.rightSection
    width: px(500)
    display: flex
    flex-direction: column
.indicateContainer
    height: px(230)
.indicate
    margin-top: px(40)
    display: flex
    justify-content: space-between
.chartsContainer
    margin-top: px(10)
    height: px(600)
    .chartRow
        margin-top: px(20)
        display: flex
        align-items: center
        gap: px(10)
        height: px(180)
    .labelContainer
        display: flex
        height: 100%
        .labelBar
            width: px(4)
        .label
            width: px(50)
            writing-mode: vertical-lr
            display: flex
            align-items: center
            justify-content: center
            font-size: px(16)
            padding: px(10) 0
            border-radius: 0 px(4) px(4) 0
    .chartContent
        flex: 1
        height: 100%

    .activePower
        .labelContainer
            .labelBar
                background: #19A3DF
            .label
                color: #19A3DF
                background: rgba(25, 163, 223, 0.1)

    .reactivePower
        .labelContainer
            .labelBar
                background: #00FF00
            .label
                color: #00FF00
                background: rgba(0, 255, 0, 0.1)

    .solarPower
        .labelContainer
            .labelBar
                background: #FFD700
            .label
                color: #FFD700
                background: rgba(255, 215, 0, 0.1)
.subtitle
    margin-top: px(15)
.map
    width: 100%
    height: calc( 100% - px(84) )
