@import '@/utils/helpers.sass'

.container
  display: grid
  grid-template-columns: 1fr 2fr 1fr
  
  width: 100%
  height: 100%  
 
 
  color: white
  

.leftSection, .rightSection
  display: flex
  flex-direction: column
 
  .indicateContainer
  
  
    height: px(250)
    .indicate
      margin-top: px(40)
  
  .chartsContainer
   
    height: px(600)
  
    .chartRow
      margin-top: px(10)
      display: flex
      align-items: center
      gap: px(10)

      height: px(170)
      .labelContainer
        display: flex
        height: 100%
        .labelBar
          width: px(4)
        .label
          width: px(50)
          writing-mode: vertical-lr
          display: flex
          align-items: center
          justify-content: center
          font-size: px(16)
          padding: px(10) 0
          border-radius: 0 px(4) px(4) 0
      .chartContent
        flex: 1
        height: 100%
    
    .activePower
      .labelContainer
        .labelBar
          background: #19A3DF
        .label
          color: #19A3DF
          background: rgba(25, 163, 223, 0.1)
    
    .reactivePower
      .labelContainer
        .labelBar
          background: #00FF00
        .label
          color: #00FF00
          background: rgba(0, 255, 0, 0.1)
    
    .solarPower
      .labelContainer
        .labelBar
          background: #FFD700
        .label
          color: #FFD700
          background: rgba(255, 215, 0, 0.1)

.indicate
  display: flex
  justify-content: space-between
  margin-top: px(25)

.middleSection
  display: flex
  flex-direction: column
  gap: px(5)
  height: 100%

  .mapSection
    height: px(630)
    background: rgba(3, 16, 52, 0.8)
    border: px(1) solid rgba(5, 126, 255, 0.3)
    border-radius: px(4)

  .infoSection
    display: grid
    grid-template-columns: 1fr 1fr
    gap: px(20)
 
  

 
 
.infoSection
  display: flex
  flex-direction: column
  gap: px(10)
  height: px(300)

  .indicators, .alerts
    
    height: px(300)
    display: flex
    flex-direction: column
  

// 统计数据容器
.statsContainer
  display: grid
  grid-template-columns: 1fr 1fr
  gap: px(15)
  margin-top: px(15)
  flex: 1

// 统计项目
.statItem
  background: rgba(255, 255, 255, 0.08)
  border-radius: px(8)
  padding: px(15)
  display: flex
  align-items: center
  gap: px(12)
  border: px(1) solid rgba(255, 255, 255, 0.1)
  transition: all 0.3s ease
  &:hover
    background: rgba(255, 255, 255, 0.12)
    border-color: rgba(255, 255, 255, 0.2)

// 统计图标
.statIcon
  width: px(48)
  height: px(48)
  border-radius: px(8)
  background: linear-gradient(135deg, rgba(65, 167, 250, 0.3), rgba(89, 177, 250, 0.2))
  display: flex
  align-items: center
  justify-content: center
  flex-shrink: 0

.iconPlaceholder
  font-size: px(20)
  color: rgba(255, 255, 255, 0.9)
  font-weight: bold

// 统计内容
.statContent
  flex: 1
  display: flex
  flex-direction: column
  gap: px(2)

.statLabel
  color: rgba(255, 255, 255, 0.7)
  font-size: px(12)
  line-height: 1

.statValue
  color: white
  font-size: px(20)
  font-weight: bold
  line-height: 1.2

.statUnit
  color: rgba(255, 255, 255, 0.6)
  font-size: px(10)
  line-height: 1

// 预警信息容器
.alertsContainer
  flex: 1
  margin-top: px(15)
  background: rgba(0, 0, 0, 0.2)
  border-radius: px(4)
  padding: px(15)
  overflow: hidden
  position: relative

// 滚动内容容器
.scrollContent
  animation: scrollUp 20s linear infinite
  &:hover
    animation-play-state: paused

// 滚动动画
@keyframes scrollUp
  0%
    transform: translateY(0)
  100%
    transform: translateY(-50%)

// 预警项目样式
.alertItem
  margin-bottom: px(15)
  opacity: 1
  &:last-child
    margin-bottom: px(15)

// 时间样式
.alertTime
  color: rgba(65, 167, 250, 0.9)
  font-size: px(12)
  margin-bottom: px(8)
  display: flex
  align-items: center
  &::after
    content: "▶▶▶"
    margin-left: px(10)
    color: rgba(65, 167, 250, 0.6)
    font-size: px(10)

// 预警内容
.alertContent
  display: flex
  align-items: flex-start
  gap: px(10)
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))
  padding: px(10) px(12)
  border-radius: px(4)
  border-left: px(3) solid rgba(65, 167, 250, 0.5)
  cursor: pointer
  transition: all 0.3s ease
  &:hover
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))
    border-left-color: rgba(65, 167, 250, 0.8)
    transform: translateX(px(3))

// 预警等级
.alertLevel
  flex-shrink: 0
  padding: px(2) px(8)
  border-radius: px(3)
  font-size: px(10)
  font-weight: bold
  color: white
  display: flex
  align-items: center
  min-width: px(35)
  justify-content: center

// 等级颜色
.level1
  background: linear-gradient(135deg, #ff4757, #ff3742)
  box-shadow: 0 0 px(8) rgba(255, 71, 87, 0.4)

.level2
  background: linear-gradient(135deg, #ffa502, #ff9500)
  box-shadow: 0 0 px(8) rgba(255, 165, 2, 0.4)

// 预警文本
.alertText
  color: rgba(255, 255, 255, 0.9)
  font-size: px(12)
  line-height: 1.4
  flex: 1
  
.rightSection
  margin-left: px(10)
  .content
    margin-bottom: px(20)
    .statusOverview
      display: flex
      gap: px(40)
      margin-top: px(10)
      padding: px(10)
      background: rgba(230, 0, 0, 0.05)
 

   
      
      .statusItem
        display: flex
        flex-direction: column
        align-items: center
      
        
        .statusLabel
          color: rgba(255, 255, 255, 0.7)
          font-size: px(14)
        
        .statusValue
          color: #19A3DF
          font-size: px(24)
          font-weight: bold
    
    .deviceList
      margin-top: px(5)
      margin-bottom: px(5)
  
      display: flex
      flex-direction: column
      gap: px(5)
    
      
      .deviceItem
        display: flex
        align-items: center
        gap: px(15)
        padding: px(15)
        background: rgba(255, 255, 255, 0.05)
        border-radius: px(4)
        border: 1px solid rgba(5, 126, 255, 0.3)
        
        .deviceIcon
          width: px(40)
          height: px(40)
          img
            width: 100%
            height: 100%
            object-fit: contain
        
        .deviceInfo
          flex: 1
          .deviceName
            font-size: px(16)
            color: white
            margin-bottom: px(8)
          
          .deviceStatus
            display: flex
            gap: px(10)
            
            .statusTag
              padding: px(2) px(10)
              border-radius: px(2)
              font-size: px(12)
              background: rgba(25, 163, 223, 0.2)
              color: #19A3DF
              
              &.charging
                background: rgba(0, 255, 0, 0.2)
                color: #00FF00
        
        .deviceMetrics
          display: flex
          gap: px(15)
          
          .metric
            font-size: px(16)
            color: white
            font-weight: bold
  