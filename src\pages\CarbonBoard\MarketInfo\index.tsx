import CustomTitle from '@/components/CustomTitle';
import styles from '../index.sass';
export default function () {
  return (
    <div className={styles.bottomSection}>
      <CustomTitle>国内碳交易主要市场信息滚动展示</CustomTitle>
      <div className={styles.marketContainer}>
        <div className={styles.scrollContent}>
          <div className={styles.marketItem}>
            <div className={styles.marketTime}>2025-03-23 11:37:25</div>
            <div className={styles.marketContent}>
              <span className={styles.marketLevel + ' ' + styles.level1}>
                I 级
              </span>
              <span className={styles.marketText}>
                全国碳市场今日成交量创新高，碳价突破120元/吨，较昨日上涨8.5%！
              </span>
            </div>
          </div>
          <div className={styles.marketItem}>
            <div className={styles.marketTime}>2025-03-23 09:37:25</div>
            <div className={styles.marketContent}>
              <span className={styles.marketLevel + ' ' + styles.level2}>
                II 级
              </span>
              <span className={styles.marketText}>
                北京环境交易所发布碳中和债券指数，为绿色金融提供新基准
              </span>
            </div>
          </div>

          <div className={styles.marketItem}>
            <div className={styles.marketTime}>2025-03-22 15:22:18</div>
            <div className={styles.marketContent}>
              <span className={styles.marketLevel + ' ' + styles.level1}>
                I 级
              </span>
              <span className={styles.marketText}>
                上海碳交易试点市场启动CCER交易，首日成交额超过2000万元！
              </span>
            </div>
          </div>

          <div className={styles.marketItem}>
            <div className={styles.marketTime}>2025-03-22 13:15:42</div>
            <div className={styles.marketContent}>
              <span className={styles.marketLevel + ' ' + styles.level2}>
                II 级
              </span>
              <span className={styles.marketText}>
                深圳碳市场推出碳期货产品，为企业提供更多风险管理工具
              </span>
            </div>
          </div>

          <div className={styles.marketItem}>
            <div className={styles.marketTime}>2025-03-21 10:30:25</div>
            <div className={styles.marketContent}>
              <span className={styles.marketLevel + ' ' + styles.level1}>
                I 级
              </span>
              <span className={styles.marketText}>
                广东碳市场与港澳地区达成合作协议，共建大湾区碳交易体系！
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
