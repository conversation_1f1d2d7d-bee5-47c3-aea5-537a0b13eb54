import CustomSelectV2 from '@/components/CustomSelectV2';
import CustomTitle from '@/components/CustomTitle';
import styles from '../index.sass';
export default function () {
  return (
    <div className={styles.topSection}>
      <div className={styles.sectionHeader}>
        <CustomTitle>碳排放统计</CustomTitle>
        <div className={styles.timeSelector}>
          <CustomSelectV2
            className={styles.timeSelect}
            defaultValue="today"
            options={[
              { label: '今日', value: 'today' },
              { label: '本周', value: 'week' },
              { label: '本月', value: 'month' },
              { label: '本年', value: 'year' },
            ]}
          />
        </div>
      </div>
      <div className={styles.statsContainer}>
        {/* 四个碳数据指标 */}
        <div className={styles.statItem}>
          <div className={styles.statIcon}>
            {/* 碳排放总量图标 - 关键词: carbon emission total / 碳排放 */}
            <div className={styles.iconPlaceholder}>CO₂</div>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statLabel}>碳排放总量</div>
            <div className={styles.statValue}>2,000</div>
            <div className={styles.statUnit}>吨CO₂</div>
          </div>
        </div>

        <div className={styles.statItem}>
          <div className={styles.statIcon}>
            {/* 碳吸收量图标 - 关键词: carbon absorption / 碳吸收 */}
            <div className={styles.iconPlaceholder}>🌱</div>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statLabel}>碳吸收量</div>
            <div className={styles.statValue}>700</div>
            <div className={styles.statUnit}>吨CO₂</div>
          </div>
        </div>

        <div className={styles.statItem}>
          <div className={styles.statIcon}>
            {/* 净碳排放图标 - 关键词: net carbon emission / 净碳排放 */}
            <div className={styles.iconPlaceholder}>⚖️</div>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statLabel}>净碳排放</div>
            <div className={styles.statValue}>1,288</div>
            <div className={styles.statUnit}>吨CO₂</div>
          </div>
        </div>

        <div className={styles.statItem}>
          <div className={styles.statIcon}>
            {/* 碳强度图标 - 关键词: carbon intensity / 碳强度 */}
            <div className={styles.iconPlaceholder}>📊</div>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statLabel}>碳强度</div>
            <div className={styles.statValue}>30</div>
            <div className={styles.statUnit}>kg/万元</div>
          </div>
        </div>
      </div>
    </div>
  );
}
