import { px } from '@/utils/util';
import { ConfigProvider } from 'antd';
import { useState } from 'react';
import DataList from './DataList';
import Dispatch from './Dispatch';
import styles from './index.sass';
import Predict from './Predict';
import Scene from './Scene';
const tabs = [
  {
    name: '韧性评估记录',
    subName: '',
    icon: '/icon4.png',
    content: <DataList></DataList>,
  },
  {
    name: '数据输入',
    subName: '预测分析',
    icon: '/icon5.png',
    content: <Predict></Predict>,
  },
  {
    name: '场景生成',
    subName: '韧性评估',
    icon: '/icon6.png',
    content: <Scene></Scene>,
  },
  {
    name: '韧性调度',
    subName: '调度执行',
    icon: '/icon7.png',
    content: <Dispatch></Dispatch>,
  },
];
export default function () {
  const [current, setCurrent] = useState(0);
  return (
    <ConfigProvider
      theme={{
        token: {
          /* 这里是你的全局 token */
          fontSize: px(18),
          controlHeight: px(35),
        },
      }}
    >
      <div className={styles.box}>
        <div className={styles.tabs}>
          {tabs.map((item, index) => {
            return (
              <div
                key={item.name}
                className={
                  styles.item + ' ' + (index === current ? styles.active : '')
                }
                onClick={() => setCurrent(index)}
              >
                <img className={styles.itemIcon} src={item.icon} alt="" />
                {item.name}
                {item.subName && (
                  <img className={styles.arrow} src="/icon2.png" alt="" />
                )}
                {item.subName || ''}
              </div>
            );
          })}
        </div>
        <div className={styles.tabContent}>
          {tabs.find((item, index) => index === current)?.content}
        </div>
      </div>
    </ConfigProvider>
  );
}
